from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import cmac
from cryptography.hazmat.backends import default_backend

def main(seed):
    key = bytes.fromhex('8dd6887134c2b5758e215f14cec669d1')
    
    # 创建 CMAC 对象，使用 AES 算法
    c = cmac.CMAC(algorithms.AES(key), backend=default_backend())
    c.update(bytes.fromhex(seed))
    
    # 计算 CMAC 值
    mac = c.finalize()
    return mac

# 示例调用
print(main("27f5dc12").hex())
