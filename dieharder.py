#!/usr/bin/env python3
"""
Python implementation of dieharder - Random Number Generator Testing Suite
A comprehensive statistical test suite for evaluating the quality of random number generators.

This implementation includes:
- Diehard battery of tests
- NIST Statistical Test Suite (STS) tests  
- RGB tests
- DAB tests

Usage:
    python dieharder.py -a                    # Run all tests
    python dieharder.py -g mt19937            # Specify generator
    python dieharder.py -t diehard_birthdays  # Run specific test
    python dieharder.py -l                    # List available tests
"""

import sys
import argparse
import time
import random
from typing import Dict, List, Optional, Tuple
import numpy as np

from generators.base_generator import BaseGenerator
from generators.mt19937 import MT19937Generator
from tests.diehard_tests import DieHardTests
from tests.sts_tests import STSTests
from tests.rgb_tests import RGBTests
from tests.dab_tests import DABTests
from utils.statistics import StatisticsUtils


class DieHarder:
    """Main DieHarder testing framework"""
    
    VERSION = "3.31.1 (Python Implementation)"
    
    def __init__(self):
        self.generators = {
            'mt19937': MT19937Generator,
        }
        
        self.test_suites = {
            'diehard': DieHardTests,
            'sts': STSTests,
            'rgb': RGBTests,
            'dab': DABTests,
        }
        
        self.current_generator: Optional[BaseGenerator] = None
        self.results: List[Dict] = []
        
    def print_header(self, generator_name: str, seed: int):
        """Print the dieharder header information"""
        print("=" * 77)
        print(f"            dieharder version {self.VERSION}")
        print("=" * 77)
        
        if self.current_generator:
            rands_per_sec = self.current_generator.get_performance()
            print(f"   rng_name    |rands/second|   Seed   |")
            print(f"        {generator_name:8s}|  {rands_per_sec:.2e}  | {seed:9d}|")
        
        print("=" * 77)
        print("        test_name   |ntup| tsamples |psamples|  p-value |Assessment")
        print("=" * 77)
    
    def setup_generator(self, generator_name: str, seed: Optional[int] = None) -> bool:
        """Setup the random number generator"""
        if generator_name not in self.generators:
            print(f"Error: Unknown generator '{generator_name}'")
            print(f"Available generators: {', '.join(self.generators.keys())}")
            return False
            
        if seed is None:
            seed = int(time.time() * 1000000) % (2**32)
            
        generator_class = self.generators[generator_name]
        self.current_generator = generator_class(seed)
        return True
    
    def assess_p_value(self, p_value: float) -> str:
        """Assess the p-value and return assessment string"""
        if p_value < 0.01 or p_value > 0.99:
            return "FAILED"
        elif p_value < 0.05 or p_value > 0.95:
            return "WEAK"
        else:
            return "PASSED"
    
    def run_test(self, test_name: str, **kwargs) -> Optional[Dict]:
        """Run a specific test"""
        if not self.current_generator:
            print("Error: No generator configured")
            return None
            
        # Find which test suite contains this test
        for suite_name, suite_class in self.test_suites.items():
            suite = suite_class(self.current_generator)
            if hasattr(suite, test_name):
                test_method = getattr(suite, test_name)
                try:
                    result = test_method(**kwargs)
                    if result:
                        result['assessment'] = self.assess_p_value(result['p_value'])
                        self.results.append(result)
                        self.print_test_result(result)
                        return result
                except Exception as e:
                    print(f"Error running test {test_name}: {e}")
                    return None
                    
        print(f"Error: Test '{test_name}' not found")
        return None
    
    def print_test_result(self, result: Dict):
        """Print a single test result"""
        name = result.get('name', 'unknown')
        ntup = result.get('ntup', 0)
        tsamples = result.get('tsamples', 0)
        psamples = result.get('psamples', 0)
        p_value = result.get('p_value', 0.0)
        assessment = result.get('assessment', 'UNKNOWN')
        
        print(f"{name:>20s}|{ntup:4d}|{tsamples:10d}|{psamples:8d}|{p_value:10.8f}|{assessment:>10s}")
    
    def list_tests(self):
        """List all available tests"""
        print("Available tests:")
        print("=" * 50)
        
        for suite_name, suite_class in self.test_suites.items():
            print(f"\n{suite_name.upper()} Tests:")
            suite = suite_class(None)  # Create instance without generator for listing
            methods = [method for method in dir(suite) 
                      if not method.startswith('_') and callable(getattr(suite, method))]
            for method in sorted(methods):
                if not method in ['__init__']:
                    print(f"  - {method}")
    
    def run_all_tests(self):
        """Run all available tests"""
        print("Running all tests...")
        
        # Diehard tests
        diehard = DieHardTests(self.current_generator)
        diehard_methods = [
            'diehard_birthdays', 'diehard_operm5', 'diehard_rank_32x32', 
            'diehard_rank_6x8', 'diehard_bitstream', 'diehard_opso',
            'diehard_oqso', 'diehard_dna', 'diehard_count_1s_str',
            'diehard_count_1s_byt', 'diehard_parking_lot', 'diehard_2dsphere',
            'diehard_3dsphere', 'diehard_squeeze', 'diehard_sums',
            'diehard_runs', 'diehard_craps'
        ]
        
        for method_name in diehard_methods:
            if hasattr(diehard, method_name):
                self.run_test(method_name)
        
        # STS tests
        sts = STSTests(self.current_generator)
        sts_methods = ['sts_monobit', 'sts_runs', 'sts_serial']
        
        for method_name in sts_methods:
            if hasattr(sts, method_name):
                self.run_test(method_name)
        
        # RGB tests
        rgb = RGBTests(self.current_generator)
        rgb_methods = [
            'rgb_bitdist', 'rgb_minimum_distance', 'rgb_permutations', 
            'rgb_lagged_sum', 'rgb_kstest_test'
        ]
        
        for method_name in rgb_methods:
            if hasattr(rgb, method_name):
                self.run_test(method_name)
        
        # DAB tests
        dab = DABTests(self.current_generator)
        dab_methods = [
            'dab_bytedistrib', 'dab_dct', 'dab_filltree', 
            'dab_filltree2', 'dab_monobit2'
        ]
        
        for method_name in dab_methods:
            if hasattr(dab, method_name):
                self.run_test(method_name)


def main():
    parser = argparse.ArgumentParser(
        description='DieHarder - Random Number Generator Testing Suite',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python dieharder.py -a                    # Run all tests
  python dieharder.py -g mt19937            # Use MT19937 generator
  python dieharder.py -t diehard_birthdays  # Run specific test
  python dieharder.py -l                    # List available tests
        """
    )
    
    parser.add_argument('-a', '--all', action='store_true',
                       help='Run all tests')
    parser.add_argument('-g', '--generator', default='mt19937',
                       help='Random number generator to use (default: mt19937)')
    parser.add_argument('-s', '--seed', type=int,
                       help='Seed for random number generator')
    parser.add_argument('-t', '--test',
                       help='Run specific test')
    parser.add_argument('-l', '--list', action='store_true',
                       help='List available tests')
    parser.add_argument('--tsamples', type=int,
                       help='Number of test samples')
    parser.add_argument('--psamples', type=int, default=100,
                       help='Number of p-value samples (default: 100)')
    
    args = parser.parse_args()
    
    dieharder = DieHarder()
    
    if args.list:
        dieharder.list_tests()
        return
    
    # Setup generator
    if not dieharder.setup_generator(args.generator, args.seed):
        sys.exit(1)
    
    # Print header
    seed = args.seed if args.seed else dieharder.current_generator.seed
    dieharder.print_header(args.generator, seed)
    
    # Run tests
    if args.all:
        dieharder.run_all_tests()
    elif args.test:
        kwargs = {}
        if args.tsamples:
            kwargs['tsamples'] = args.tsamples
        if args.psamples:
            kwargs['psamples'] = args.psamples
        dieharder.run_test(args.test, **kwargs)
    else:
        print("No test specified. Use -a for all tests, -t for specific test, or -l to list tests.")
        sys.exit(1)


if __name__ == "__main__":
    main()
