import socket
import time
import binascii
import base64
sk = socket.socket()
sk.connect(('192.168.18.155', 13400))

def send_and_rcv(data):
    sk.send(binascii.a2b_hex(data))
    msg = sk.recv(1024)
    print('send:    ',data)
    print('Receive: ',binascii.b2a_hex(msg).decode('utf-8'))
    return msg

data = '02fd0005000000070e800000000000'
send_and_rcv(data)
diddata = '02FD8001000000070E80000122F1A0'
send_and_rcv(diddata)
sessiondata = '02FD8001000000060E8000011003'
send_and_rcv(sessiondata)
diddata = '02FD8001000000070E80000122F1A0'
send_and_rcv(diddata)
sessiondata = '02FD8001000000060E8000011003'
send_and_rcv(sessiondata)
diddata = '02FD8001000000070E80000122F172'
msg = send_and_rcv(diddata)

diddata = binascii.b2a_hex(msg).decode('utf-8').split('62f172')[1]
print('F172: ', diddata)
diddata = bytes.fromhex(diddata)
diddata = diddata.decode('ascii')
print('F172 str: ',diddata)
decoded_bytes = base64.b64decode(diddata)
decoded_string = decoded_bytes.decode('utf-8')
print('F172 base64 decode:\n',decoded_string)

# 过27服务
sessiondata = '02FD8001000000060E8000011003'
send_and_rcv(sessiondata)

securitydata = '02FD8001000000060E8000012701'
msg = send_and_rcv(securitydata)
seed = binascii.b2a_hex(msg).decode('utf-8').split('6701')[1]
print()
print('Seed: ',seed)
n = 627585038806247
d = 119987789848673
key = pow(int(seed,16),d,n)
key = hex(key)[2:]

if len(key) % 2 == 1:
    key = key.rjust(len(key)+1,'0')
print('key:  ',key)
print()
datalength = 6 + len(key)//2
datalength = hex(datalength)[2:].rjust(2,'0')
keydata = '02FD8001000000' + datalength + '0E8000012702'+ key  
send_and_rcv(keydata)
diddata = '02FD8001000000070E80000122F1A0'
send_and_rcv(diddata)



sk.close()