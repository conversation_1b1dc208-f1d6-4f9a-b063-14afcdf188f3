#!/usr/bin/env python3
"""
GNU Radio信号播放器
用于播放TPMS生成的信号文件
"""

import numpy as np
import matplotlib.pyplot as plt
import argparse
import os

def load_signal(filename):
    """加载信号文件"""
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return None
    
    try:
        # 加载复数信号 (交错的I/Q数据)
        data = np.fromfile(filename, dtype=np.float32)
        
        # 将交错数据转换为复数
        if len(data) % 2 != 0:
            print("警告: 数据长度不是偶数，截断最后一个样本")
            data = data[:-1]
        
        complex_data = data[0::2] + 1j * data[1::2]
        
        print(f"加载信号文件: {filename}")
        print(f"数据长度: {len(complex_data)} 个复数样本")
        print(f"文件大小: {len(data) * 4} bytes")
        
        return complex_data
    
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def analyze_signal(signal, sample_rate=2e6):
    """分析信号特性"""
    print("\n=== 信号分析 ===")
    
    # 基本统计
    print(f"样本数量: {len(signal)}")
    print(f"信号持续时间: {len(signal)/sample_rate:.3f} 秒")
    print(f"最大幅度: {np.max(np.abs(signal)):.3f}")
    print(f"平均功率: {np.mean(np.abs(signal)**2):.3f}")
    
    # 频谱分析
    fft = np.fft.fft(signal)
    freqs = np.fft.fftfreq(len(signal), 1/sample_rate)
    power_spectrum = np.abs(fft)**2
    
    # 找到主要频率分量
    peak_idx = np.argmax(power_spectrum[:len(power_spectrum)//2])
    peak_freq = freqs[peak_idx]
    
    print(f"主要频率分量: {peak_freq/1e6:.2f} MHz")
    
    return {
        'signal': signal,
        'fft': fft,
        'freqs': freqs,
        'power_spectrum': power_spectrum,
        'peak_freq': peak_freq
    }

def plot_signal(analysis_result, save_plots=False):
    """绘制信号图形"""
    signal = analysis_result['signal']
    fft = analysis_result['fft']
    freqs = analysis_result['freqs']
    power_spectrum = analysis_result['power_spectrum']
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('TPMS信号分析', fontsize=16)
    
    # 时域信号 - 实部
    axes[0, 0].plot(signal.real[:1000])
    axes[0, 0].set_title('时域信号 - 实部 (前1000个样本)')
    axes[0, 0].set_xlabel('样本')
    axes[0, 0].set_ylabel('幅度')
    axes[0, 0].grid(True)
    
    # 时域信号 - 虚部
    axes[0, 1].plot(signal.imag[:1000])
    axes[0, 1].set_title('时域信号 - 虚部 (前1000个样本)')
    axes[0, 1].set_xlabel('样本')
    axes[0, 1].set_ylabel('幅度')
    axes[0, 1].grid(True)
    
    # 功率谱密度
    axes[1, 0].semilogy(freqs[:len(freqs)//2]/1e6, power_spectrum[:len(power_spectrum)//2])
    axes[1, 0].set_title('功率谱密度')
    axes[1, 0].set_xlabel('频率 (MHz)')
    axes[1, 0].set_ylabel('功率')
    axes[1, 0].grid(True)
    
    # 星座图
    axes[1, 1].scatter(signal.real[::100], signal.imag[::100], alpha=0.6, s=1)
    axes[1, 1].set_title('星座图')
    axes[1, 1].set_xlabel('实部')
    axes[1, 1].set_ylabel('虚部')
    axes[1, 1].grid(True)
    axes[1, 1].axis('equal')
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig('tpms_signal_analysis.png', dpi=300, bbox_inches='tight')
        print("图形已保存为 tpms_signal_analysis.png")
    
    plt.show()

def generate_gnuradio_script(signal_file, output_file="tpms_transmit.grc"):
    """生成GNU Radio流图脚本"""
    grc_content = f"""<?xml version='1.0' encoding='utf-8'?>
<?grc format='1' version='*******'?>
<flow_graph>
  <timestamp>Mon Jan  1 00:00:00 2024</timestamp>
  <block>
    <key>options</key>
    <param>
      <key>author</key>
      <value>TPMS Generator</value>
    </param>
    <param>
      <key>title</key>
      <value>TPMS Signal Transmitter</value>
    </param>
    <param>
      <key>generate_options</key>
      <value>qt_gui</value>
    </param>
  </block>
  
  <block>
    <key>file_source</key>
    <param>
      <key>file</key>
      <value>{signal_file}</value>
    </param>
    <param>
      <key>type</key>
      <value>complex</value>
    </param>
    <param>
      <key>repeat</key>
      <value>True</value>
    </param>
  </block>
  
  <block>
    <key>osmosdr_sink</key>
    <param>
      <key>sample_rate</key>
      <value>2000000</value>
    </param>
    <param>
      <key>center_freq</key>
      <value>433920000</value>
    </param>
    <param>
      <key>freq_corr</key>
      <value>0</value>
    </param>
    <param>
      <key>gain</key>
      <value>47</value>
    </param>
  </block>
</flow_graph>"""
    
    with open(output_file, 'w') as f:
        f.write(grc_content)
    
    print(f"GNU Radio流图已生成: {output_file}")
    print("使用 gnuradio-companion 打开此文件进行信号发送")

def main():
    parser = argparse.ArgumentParser(description='GNU Radio TPMS信号播放器')
    parser.add_argument('--file', default='tpms_signal.bin', help='信号文件路径')
    parser.add_argument('--analyze', action='store_true', help='分析信号')
    parser.add_argument('--plot', action='store_true', help='绘制信号图形')
    parser.add_argument('--save-plots', action='store_true', help='保存图形到文件')
    parser.add_argument('--generate-grc', action='store_true', help='生成GNU Radio流图')
    parser.add_argument('--sample-rate', type=float, default=2e6, help='采样率')
    
    args = parser.parse_args()
    
    # 加载信号
    signal = load_signal(args.file)
    if signal is None:
        return
    
    # 分析信号
    if args.analyze or args.plot:
        analysis = analyze_signal(signal, args.sample_rate)
        
        if args.plot:
            plot_signal(analysis, args.save_plots)
    
    # 生成GNU Radio流图
    if args.generate_grc:
        generate_gnuradio_script(os.path.abspath(args.file))
    
    print("\n=== 使用说明 ===")
    print("1. 使用GNU Radio Companion打开生成的.grc文件")
    print("2. 连接HackRF设备")
    print("3. 运行流图开始发送信号")
    print("4. 注意：请遵守当地法律法规，仅用于研究目的")

if __name__ == "__main__":
    main()
