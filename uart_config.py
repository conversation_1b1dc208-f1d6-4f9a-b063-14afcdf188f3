#!/usr/bin/env python3
"""
UART嗅探器配置文件
包含各种预设配置和工具函数
"""

# 默认配置
DEFAULT_CONFIG = {
    'port': 'COM3',
    'baudrate': 115200,
    'timeout': 0.1,
    'idle_timeout': 0.5,
    'send_data': b'\x01\x02\x03\x04',
    'buffer_size': 1024,
    'log_level': 'INFO'
}

# 常用波特率
COMMON_BAUDRATES = [
    9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600
]

# 预设配置
PRESET_CONFIGS = {
    'debug': {
        'port': 'COM3',
        'baudrate': 115200,
        'idle_timeout': 0.1,  # 快速响应
        'send_data': b'\xAA\xBB\xCC\xDD',
        'log_level': 'DEBUG'
    },
    
    'production': {
        'port': 'COM1',
        'baudrate': 921600,
        'idle_timeout': 1.0,  # 稳定响应
        'send_data': b'\x55\xAA\x55\xAA',
        'log_level': 'INFO'
    },
    
    'slow_device': {
        'port': 'COM2',
        'baudrate': 9600,
        'idle_timeout': 2.0,  # 慢速设备
        'send_data': b'\xFF\x00\xFF\x00',
        'log_level': 'INFO'
    },
    
    'high_speed': {
        'port': 'COM4',
        'baudrate': 921600,
        'idle_timeout': 0.05,  # 高速响应
        'send_data': b'\x12\x34\x56\x78',
        'log_level': 'WARNING'
    }
}

# 测试数据模式
TEST_DATA_PATTERNS = {
    'counter': lambda n: bytes([i % 256 for i in range(n)]),
    'alternating': lambda n: bytes([0xAA if i % 2 == 0 else 0x55 for i in range(n)]),
    'random': lambda n: bytes([hash(i) % 256 for i in range(n)]),
    'ascii': lambda n: b'Hello UART Test ' * (n // 16 + 1)[:n],
    'binary': lambda n: (b'\x00\xFF' * (n // 2 + 1))[:n]
}


def get_config(preset_name=None):
    """获取配置"""
    if preset_name and preset_name in PRESET_CONFIGS:
        config = DEFAULT_CONFIG.copy()
        config.update(PRESET_CONFIGS[preset_name])
        return config
    return DEFAULT_CONFIG.copy()


def validate_config(config):
    """验证配置"""
    errors = []
    
    # 检查必需字段
    required_fields = ['port', 'baudrate', 'idle_timeout', 'send_data']
    for field in required_fields:
        if field not in config:
            errors.append(f"缺少必需字段: {field}")
    
    # 检查波特率
    if 'baudrate' in config:
        if not isinstance(config['baudrate'], int) or config['baudrate'] <= 0:
            errors.append("波特率必须是正整数")
    
    # 检查空闲超时
    if 'idle_timeout' in config:
        if not isinstance(config['idle_timeout'], (int, float)) or config['idle_timeout'] <= 0:
            errors.append("空闲超时必须是正数")
    
    # 检查发送数据
    if 'send_data' in config:
        if not isinstance(config['send_data'], bytes):
            errors.append("发送数据必须是bytes类型")
    
    return errors


def create_test_data(pattern='counter', length=4):
    """创建测试数据"""
    if pattern in TEST_DATA_PATTERNS:
        return TEST_DATA_PATTERNS[pattern](length)
    else:
        return TEST_DATA_PATTERNS['counter'](length)


def list_available_ports():
    """列出可用串口"""
    try:
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        return [(port.device, port.description) for port in ports]
    except ImportError:
        print("需要安装pyserial: pip install pyserial")
        return []


def print_config(config):
    """打印配置信息"""
    print("当前配置:")
    print(f"  串口: {config.get('port', 'N/A')}")
    print(f"  波特率: {config.get('baudrate', 'N/A')}")
    print(f"  空闲超时: {config.get('idle_timeout', 'N/A')}秒")
    
    send_data = config.get('send_data', b'')
    if isinstance(send_data, bytes):
        print(f"  发送数据: {send_data.hex()} ({len(send_data)} bytes)")
    else:
        print(f"  发送数据: {send_data}")
    
    print(f"  日志级别: {config.get('log_level', 'INFO')}")


def interactive_config():
    """交互式配置"""
    print("=== UART嗅探器配置 ===")
    
    # 显示可用串口
    ports = list_available_ports()
    if ports:
        print("\n可用串口:")
        for i, (device, desc) in enumerate(ports, 1):
            print(f"  {i}. {device} - {desc}")
    
    config = DEFAULT_CONFIG.copy()
    
    # 串口配置
    port_input = input(f"\n串口号 (默认: {config['port']}): ").strip()
    if port_input:
        config['port'] = port_input
    
    # 波特率配置
    print(f"\n常用波特率: {', '.join(map(str, COMMON_BAUDRATES))}")
    baudrate_input = input(f"波特率 (默认: {config['baudrate']}): ").strip()
    if baudrate_input:
        try:
            config['baudrate'] = int(baudrate_input)
        except ValueError:
            print("无效波特率，使用默认值")
    
    # 空闲超时配置
    timeout_input = input(f"空闲超时/秒 (默认: {config['idle_timeout']}): ").strip()
    if timeout_input:
        try:
            config['idle_timeout'] = float(timeout_input)
        except ValueError:
            print("无效超时值，使用默认值")
    
    # 发送数据配置
    print(f"\n测试数据模式: {', '.join(TEST_DATA_PATTERNS.keys())}")
    data_mode = input("选择数据模式 (默认: counter): ").strip() or 'counter'
    data_length = input("数据长度 (默认: 4): ").strip()
    
    try:
        length = int(data_length) if data_length else 4
        config['send_data'] = create_test_data(data_mode, length)
    except ValueError:
        print("无效长度，使用默认数据")
    
    # 验证配置
    errors = validate_config(config)
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
        return None
    
    print("\n配置完成:")
    print_config(config)
    
    return config


if __name__ == "__main__":
    print("UART嗅探器配置工具")
    print("\n预设配置:")
    for name, preset in PRESET_CONFIGS.items():
        print(f"  {name}: {preset['port']} @ {preset['baudrate']} bps")
    
    print("\n选择操作:")
    print("  1. 交互式配置")
    print("  2. 查看预设配置")
    print("  3. 列出可用串口")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        config = interactive_config()
        if config:
            print("\n配置已生成，可以在代码中使用")
    elif choice == "2":
        for name, preset in PRESET_CONFIGS.items():
            print(f"\n=== {name.upper()} ===")
            print_config(get_config(name))
    elif choice == "3":
        ports = list_available_ports()
        if ports:
            print("\n可用串口:")
            for device, desc in ports:
                print(f"  {device} - {desc}")
        else:
            print("\n未找到可用串口")
    else:
        print("无效选择")
