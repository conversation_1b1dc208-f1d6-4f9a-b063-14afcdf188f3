#!/usr/bin/env python3
"""
创建一个简单的测试来验证Java代码分析功能
"""

import tempfile
import zipfile
import os

# 导入我们的模块
import importlib.util
spec = importlib.util.spec_from_file_location("sm_detect", "SM detect.py")
sm_detect = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sm_detect)

APKAnalyzer = sm_detect.APKAnalyzer


def create_minimal_apk_with_manifest():
    """创建一个包含国密算法类名的最小APK"""
    temp_apk = tempfile.NamedTemporaryFile(suffix='.apk', delete=False)
    
    with zipfile.ZipFile(temp_apk.name, 'w') as apk_zip:
        # 创建一个最小的AndroidManifest.xml（二进制格式）
        # 这里我们创建一个简单的文本版本，虽然不是标准的二进制AXML格式
        manifest_content = '''<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.test.smcrypto">
    <application android:label="Test">
        <activity android:name=".SM2CryptoActivity" />
        <service android:name=".SM3HashService" />
        <receiver android:name=".SM4CipherReceiver" />
    </application>
</manifest>'''
        apk_zip.writestr('AndroidManifest.xml', manifest_content.encode('utf-8'))
        
        # 创建一个简单的DEX文件头
        # 这不是一个完整的DEX文件，但包含了基本的结构
        dex_content = bytearray(1024)
        dex_content[0:8] = b'dex\n035\x00\x00\x00'  # DEX魔数和版本
        
        # 在DEX文件中嵌入一些类名字符串
        class_names = [
            "Lcom/test/smcrypto/SM2CryptoActivity;",
            "Lcom/test/smcrypto/SM3HashService;", 
            "Lcom/test/smcrypto/SM4CipherReceiver;",
            "Lcom/crypto/sm/SM2Util;",
            "Lcom/crypto/sm/SM3Digest;",
            "Lcom/crypto/sm/SM4Engine;"
        ]
        
        offset = 100
        for class_name in class_names:
            class_bytes = class_name.encode('utf-8')
            dex_content[offset:offset+len(class_bytes)] = class_bytes
            offset += len(class_bytes) + 10
        
        apk_zip.writestr('classes.dex', bytes(dex_content))
        
        # 添加一个包含国密算法常量的SO文件
        constants = sm_detect.SMCryptoConstants()
        so_content = b'\x7fELF' + b'\x00' * 100
        
        # 添加SM4 SBox
        sm4_sbox_bytes = bytes(constants.SM4_SBOX)
        so_content += sm4_sbox_bytes
        
        # 添加一些函数名字符串
        function_names = [
            b"sm2_encrypt\x00",
            b"sm3_hash\x00", 
            b"sm4_cipher\x00",
            b"SM2_generate_key\x00",
            b"SM3_digest\x00",
            b"SM4_encrypt_block\x00"
        ]
        
        for func_name in function_names:
            so_content += func_name
        
        so_content += b'\x00' * (2048 - len(so_content))
        apk_zip.writestr('lib/arm64-v8a/libsmcrypto.so', so_content)
    
    return temp_apk.name


def test_manifest_analysis():
    """测试AndroidManifest.xml中的类名分析"""
    print("测试AndroidManifest.xml分析...")
    
    test_apk = create_minimal_apk_with_manifest()
    
    try:
        # 手动分析AndroidManifest.xml
        with zipfile.ZipFile(test_apk, 'r') as apk_zip:
            manifest_content = apk_zip.read('AndroidManifest.xml').decode('utf-8')
            
            analyzer = APKAnalyzer(test_apk)
            
            # 在manifest中查找国密算法相关的类名
            lines = manifest_content.split('\n')
            found_classes = []
            
            for line in lines:
                if 'android:name=' in line:
                    # 提取类名
                    start = line.find('"') + 1
                    end = line.rfind('"')
                    if start > 0 and end > start:
                        class_name = line[start:end]
                        if analyzer._check_crypto_keywords(class_name):
                            algorithm = analyzer._identify_algorithm(class_name)
                            found_classes.append({
                                'name': class_name,
                                'algorithm': algorithm,
                                'type': 'manifest_class'
                            })
            
            print(f"在AndroidManifest.xml中发现 {len(found_classes)} 个国密算法相关类:")
            for cls in found_classes:
                print(f"  - {cls['algorithm']}: {cls['name']}")
                
            return found_classes
            
    finally:
        os.unlink(test_apk)


def test_simple_string_analysis():
    """测试简单的字符串分析"""
    print("\n测试简单字符串分析...")
    
    test_apk = create_minimal_apk_with_manifest()
    
    try:
        analyzer = APKAnalyzer(test_apk)
        
        # 手动分析DEX文件中的字符串
        with zipfile.ZipFile(test_apk, 'r') as apk_zip:
            dex_data = apk_zip.read('classes.dex')
            
            # 简单的字符串提取（查找可打印字符序列）
            dex_str = dex_data.decode('utf-8', errors='ignore')
            
            found_strings = []
            # 查找包含国密算法关键字的字符串
            for keyword in ['SM2', 'SM3', 'SM4']:
                start = 0
                while True:
                    pos = dex_str.find(keyword, start)
                    if pos == -1:
                        break
                    
                    # 提取包含关键字的字符串片段
                    string_start = max(0, pos - 20)
                    string_end = min(len(dex_str), pos + 50)
                    string_fragment = dex_str[string_start:string_end].strip()
                    
                    if string_fragment and analyzer._check_crypto_keywords(string_fragment):
                        algorithm = analyzer._identify_algorithm(string_fragment)
                        found_strings.append({
                            'fragment': string_fragment,
                            'algorithm': algorithm,
                            'position': pos
                        })
                    
                    start = pos + 1
            
            print(f"在DEX文件中发现 {len(found_strings)} 个国密算法相关字符串:")
            for s in found_strings:
                print(f"  - {s['algorithm']}: {s['fragment'][:30]}...")
                
            return found_strings
            
    finally:
        os.unlink(test_apk)


def test_complete_analysis():
    """测试完整的分析流程"""
    print("\n测试完整分析流程...")
    
    test_apk = create_minimal_apk_with_manifest()
    
    try:
        analyzer = APKAnalyzer(test_apk)
        
        # 执行完整分析
        report = analyzer.analyze()
        
        print("完整分析报告:")
        print(f"  总候选项数: {report['analysis_summary']['total_candidates']}")
        print(f"  过滤后候选项数: {report['analysis_summary']['filtered_candidates']}")
        print(f"  验证的算法实现数: {report['analysis_summary']['verified_algorithms']}")
        
        if report['algorithms_found']:
            print("  发现的算法:")
            for algorithm, info in report['algorithms_found'].items():
                print(f"    {algorithm}: {info['count']} 个实现")
        else:
            print("  未发现算法实现")
            
        return report
        
    finally:
        os.unlink(test_apk)


def main():
    """运行所有测试"""
    print("开始测试简化的Java代码分析...")
    print("="*60)
    
    try:
        manifest_results = test_manifest_analysis()
        string_results = test_simple_string_analysis()
        complete_report = test_complete_analysis()
        
        print("="*60)
        print("✓ 简化Java代码分析测试完成")
        
        total_found = len(manifest_results) + len(string_results)
        if total_found > 0:
            print(f"✓ 总共发现 {total_found} 个国密算法相关项目")
        else:
            print("- 未发现国密算法相关项目（这是正常的，因为我们的测试APK很简单）")
        
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
