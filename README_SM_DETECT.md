# Android APK 国密算法检测系统

这是一个用于检测Android APK文件中SM2、SM3、SM4国密算法实现的自动化工具。

## 功能特性

### 核心功能
1. **国密算法特征数据集**: 包含SM2、SM3、SM4算法的特定常量
2. **APK分层分析**: 自动分离Java代码(DEX)和Native代码(.so)
3. **智能识别**: 通过特征匹配和关键字识别算法实现
4. **干扰项过滤**: 自动过滤Base64编码、Lambda表达式等干扰项
5. **算法验证**: 静态分析验证算法实现的正确性

### 检测算法
- **SM2**: 椭圆曲线公钥密码算法
- **SM3**: 密码杂凑算法
- **SM4**: 分组密码算法

## 安装依赖

### 自动安装
```bash
python install_dependencies.py
```

### 手动安装
```bash
pip install androguard pyelftools
```

### 可选依赖
```bash
pip install requests cryptography
```

## 使用方法

### 基本用法
```bash
python "SM detect.py" <apk_file_path>
```

### 高级用法
```bash
# 保存报告到JSON文件
python "SM detect.py" app.apk -o report.json

# 详细输出
python "SM detect.py" app.apk -v

# 只分析Java代码
python "SM detect.py" app.apk --no-native

# 只分析Native代码
python "SM detect.py" app.apk --no-java

# 查看帮助
python "SM detect.py" --help
```

## 检测原理

### 步骤1: 建立特征数据集
- **SM2椭圆曲线参数**: p, a, b, n, G坐标
- **SM3算法常量**: T数组、IV初始化向量
- **SM4算法常量**: SBox、CK数组、FK数组

### 步骤2: APK分层
- **Java代码部分**: DEX可执行文件分析
- **Native代码部分**: 共享目标库(.so)文件分析

### 步骤3: 代码识别
- **Java代码识别**: 使用Androguard分析方法名、类名、字符串和数值
- **Native代码识别**: 使用pyelftools分析.rodata和.data段以及导出函数

### 步骤4: 干扰项过滤
- 过滤Base64编码值（通常出现在数字证书中）
- 过滤Java Lambda表达式
- 过滤同名不同义字符串（如isM3U8、GSM2等）

### 步骤5: 算法验证
- **Java代码**: 静态分析验证，支持动态调试（需要调试环境）
- **Native代码**: 静态分析验证，检测OpenSSL库变体

## 输出报告

### 报告结构
```json
{
  "apk_file": "app.apk",
  "analysis_summary": {
    "total_candidates": 15,
    "filtered_candidates": 8,
    "verified_algorithms": 3
  },
  "algorithms_found": {
    "SM2": {
      "count": 1,
      "types": ["java_constant"],
      "confidence_levels": ["high"]
    },
    "SM4": {
      "count": 2,
      "types": ["native_constant", "native_function"],
      "confidence_levels": ["high", "medium"]
    }
  },
  "detailed_results": [...]
}
```

### 置信度说明
- **高(high)**: 匹配到算法特征常量
- **中(medium)**: 匹配到算法相关关键字

## 测试

运行测试套件：
```bash
python test_sm_detect.py
```

测试包括：
- 国密算法常量验证
- APK分析器功能测试
- 完整分析流程测试

## 技术架构

### 核心类
- `SMCryptoConstants`: 国密算法特征常量管理
- `APKAnalyzer`: APK文件分析器

### 依赖库
- `androguard`: Android应用分析
- `pyelftools`: ELF文件分析
- `zipfile`: APK文件解压
- `re`: 正则表达式匹配

## 限制说明

1. **动态验证**: 当前主要支持静态分析，动态验证需要调试环境
2. **加壳应用**: 对于加壳或混淆的应用，检测效果可能受限
3. **自定义实现**: 对于完全自定义的算法实现，可能无法检测

## 扩展功能

### 支持的验证方式
1. **静态分析**: 基于特征常量和关键字匹配
2. **动态调试**: 通过二进制插桩拦截方法调用（需要调试环境）
3. **JNI调用**: 构造测试项目调用Native方法
4. **OpenSSL变体**: 检测基于OpenSSL的国密算法实现

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

本项目采用MIT许可证。

## 更新日志

### v1.0.0 (2025-06-15)
- 初始版本发布
- 支持SM2、SM3、SM4算法检测
- 实现Java和Native代码分析
- 添加干扰项过滤功能
- 提供详细的分析报告
