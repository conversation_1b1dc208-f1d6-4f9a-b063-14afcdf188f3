import sys
import os
import random
import argparse
import socket
import time
import signal
from scapy.all import *
from construct import *
from colorama import init, Fore
from scapy.all import *
from scapy.layers.l2 import *
from scapy.layers.inet import *
import platform

# 全局变量用于优雅退出
running = True
sock = None

def signal_handler(signum, frame):
    """处理Ctrl+C信号"""
    global running, sock
    print(Fore.YELLOW + "\n\n[  中断  ] 检测到Ctrl+C，正在安全退出...")
    running = False
    if sock:
        try:
            sock.close()
            print(Fore.GREEN + "[  TCP  ] 连接已关闭")
        except:
            pass

def setup_signal_handlers():
    """设置信号处理器"""
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)

def establish_tcp_connection(target_ip, target_port, retry_count=3, retry_delay=2):
    """建立TCP连接，支持重试"""
    for attempt in range(retry_count):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(5)  # 设置超时时间
            s.connect((target_ip, target_port))
            print(Fore.GREEN + f"[  TCP  ] 成功连接到 {target_ip}:{target_port}")
            return s
        except socket.error as msg:
            if attempt < retry_count - 1:
                print(Fore.YELLOW + f"[  TCP  ] 连接失败 (尝试 {attempt + 1}/{retry_count}): {msg}")
                print(Fore.YELLOW + f"[  TCP  ] {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print(Fore.RED + f"[  TCP  ] 连接失败 (所有尝试已用完): {msg}")
    return None

def check_connection_alive(sock):
    """检查TCP连接是否仍然活跃"""
    try:
        # 发送一个空的数据包来检查连接状态
        sock.settimeout(1)
        sock.send(b'')
        return True
    except socket.error:
        return False
    except Exception:
        return False

def reconnect_with_routing_activation(target_ip, target_port):
    """重新连接并发送路由激活"""
    global sock

    print(Fore.YELLOW + "[  重连  ] 尝试重新建立TCP连接...")

    # 关闭旧连接
    if sock:
        try:
            sock.close()
        except:
            pass
        sock = None

    # 建立新连接
    sock = establish_tcp_connection(target_ip, target_port, retry_count=5, retry_delay=1)
    if not sock:
        print(Fore.RED + "[  重连  ] 无法重新建立TCP连接")
        return False

    # 发送路由激活
    if not send_routing_activation(sock):
        print(Fore.YELLOW + "[  重连  ] 路由激活失败，但继续测试")

    print(Fore.GREEN + "[  重连  ] TCP连接和路由激活完成")
    return True

def send_routing_activation(sock, timeout=3):
    """发送路由激活消息"""
    # DoIP路由激活消息: 02 FD 00 05 00 00 00 0B 0E 80 00 00 00 00 00 FF FF FF FF
    routing_activation = bytes([
        0x02, 0xFD,  # 协议版本和反向版本
        0x00, 0x05,  # 载荷类型 (路由激活请求)
        0x00, 0x00, 0x00, 0x0B,  # 载荷长度 (11字节)
        0x0E, 0x80,  # 源地址
        0x00, 0x00,  # 激活类型
        0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF  # 保留字段和OEM特定字段
    ])

    try:
        # 检查连接状态
        if not check_connection_alive(sock):
            print(Fore.RED + "[  DoIP  ] 连接已断开，无法发送路由激活")
            return False

        sock.settimeout(timeout)
        sock.send(routing_activation)
        print(Fore.GREEN + "[  DoIP  ] 路由激活消息已发送")

        # 等待响应
        try:
            response = sock.recv(1024)
            if response:
                print(Fore.CYAN + "[  DoIP  ] 收到路由激活响应:")
                response_hex = " ".join([f"{b:02X}" for b in response])
                print(Fore.CYAN + f"[  DoIP  ] 响应数据: {response_hex}")

                # 简单解析响应
                if len(response) >= 8:
                    payload_type = (response[2] << 8) | response[3]
                    if payload_type == 0x0006:  # 路由激活响应
                        if len(response) >= 9:
                            response_code = response[8]
                            if response_code == 0x10:
                                print(Fore.GREEN + "[  DoIP  ] 路由激活成功")
                                return True
                            else:
                                print(Fore.YELLOW + f"[  DoIP  ] 路由激活失败，响应码: 0x{response_code:02X}")
                                return False
                return True
            else:
                print(Fore.YELLOW + "[  DoIP  ] 未收到路由激活响应")
                return False
        except socket.timeout:
            print(Fore.YELLOW + "[  DoIP  ] 路由激活响应超时")
            return False

    except socket.error as e:
        print(Fore.RED + f"[  DoIP  ] 发送路由激活失败: {e}")
        return False
    except Exception as e:
        print(Fore.RED + f"[  DoIP  ] 路由激活异常: {e}")
        return False

def heartBeat(target_ip, target_port):
    """心跳检测"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(2)
    try:
        s.connect((target_ip, target_port))
        s.close()
        return True
    except socket.error:
        return False

def generate_fuzz_data(base_data, fuzz_type="random"):
    """生成模糊测试数据"""
    if platform.system() == "Linux":
        try:
            import pyradamsa
            rad = pyradamsa.Radamsa()
            return rad.fuzz(base_data)
        except ImportError:
            print(Fore.YELLOW + "[  FUZZ  ] pyradamsa未安装，使用随机模糊测试")

    # Windows或Linux无pyradamsa时使用随机模糊测试
    fuzzed_data = bytearray(base_data)

    if fuzz_type == "random":
        # 随机修改字节
        for _ in range(random.randint(1, 5)):
            if len(fuzzed_data) > 0:
                pos = random.randint(0, len(fuzzed_data) - 1)
                fuzzed_data[pos] = random.randint(0, 255)
    elif fuzz_type == "length":
        # 长度变异
        if random.choice([True, False]):
            # 增加长度
            fuzzed_data.extend(bytes([random.randint(0, 255) for _ in range(random.randint(1, 100))]))
        else:
            # 减少长度
            if len(fuzzed_data) > 1:
                fuzzed_data = fuzzed_data[:random.randint(1, len(fuzzed_data))]

    return bytes(fuzzed_data)

def parseArgs():
    parser = argparse.ArgumentParser(description='DoIP Fuzzer with TCP connection and routing activation')
    parser.add_argument('-t', '--target', required=True, type=str, help='The IPV4 address of target.')
    parser.add_argument('-p', '--port', required=False, type=int, default=13400, help='The port of target (default: 13400).')
    parser.add_argument('-c', '--count', required=False, type=int, default=1000, help='Number of fuzz test cases (default: 1000).')
    parser.add_argument('-d', '--delay', required=False, type=float, default=0.1, help='Delay between packets in seconds (default: 0.1).')
    parser.add_argument('-m', '--monitor', required=False, action='store_true', help='Monitor target availability.')
    parser.add_argument('-r', '--reconnect-interval', required=False, type=int, default=50, help='Check connection every N packets (default: 50).')
    parser.add_argument('--max-reconnects', required=False, type=int, default=10, help='Maximum reconnection attempts (default: 10).')
    args = parser.parse_args()
    return args

def print_connection_stats(reconnect_count, total_tests):
    """打印连接统计信息"""
    if reconnect_count > 0:
        print(Fore.CYAN + f"[  统计  ] 总重连次数: {reconnect_count}")
        print(Fore.CYAN + f"[  统计  ] 平均每 {total_tests//reconnect_count if reconnect_count > 0 else 0} 个测试重连一次")

def create_doip_message(payload_type, data=b"", source_addr=0x0E80, target_addr=0x1000):
    """创建DoIP消息"""
    # 根据不同的payload类型计算长度
    if payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息类型
        payload_length = 4 + len(data)  # 源地址(2) + 目标地址(2) + 数据长度
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
            (source_addr >> 8) & 0xFF,  # 源地址
            source_addr & 0xFF,
            (target_addr >> 8) & 0xFF,  # 目标地址
            target_addr & 0xFF,
        ])
        return doip_header + data
    else:
        # 其他类型的DoIP消息
        payload_length = len(data)
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
        ])
        return doip_header + data

def generate_random_doip_payload():
    """生成随机DoIP载荷"""
    # DoIP载荷类型定义
    payload_types = {
        0x0000: "通用DoIP头否定应答",
        0x0001: "车辆识别请求",
        0x0002: "车辆识别请求与EID",
        0x0003: "车辆识别请求与VIN",
        0x0004: "车辆公告消息",
        0x0005: "路由激活请求",
        0x0006: "路由激活响应",
        0x0007: "存活检查请求",
        0x0008: "存活检查响应",
        0x4001: "DoIP实体状态请求",
        0x4002: "DoIP实体状态响应",
        0x4003: "诊断电源模式信息请求",
        0x4004: "诊断电源模式信息响应",
        0x8001: "诊断消息",
        0x8002: "诊断消息正面应答",
        0x8003: "诊断消息否定应答",
    }

    # 随机选择载荷类型
    payload_type = random.choice(list(payload_types.keys()))

    # 根据载荷类型生成相应的数据
    if payload_type == 0x0001:  # 车辆识别请求
        data = b""  # 无数据
    elif payload_type == 0x0002:  # 车辆识别请求与EID
        data = bytes([random.randint(0, 255) for _ in range(6)])  # 6字节EID
    elif payload_type == 0x0003:  # 车辆识别请求与VIN
        data = bytes([random.randint(0x30, 0x5A) for _ in range(17)])  # 17字节VIN
    elif payload_type == 0x0005:  # 路由激活请求
        data = bytes([
            random.randint(0, 255), random.randint(0, 255),  # 源地址
            random.randint(0, 255),  # 激活类型
            0x00, 0x00, 0x00, 0x00,  # 保留
            random.randint(0, 255), random.randint(0, 255),
            random.randint(0, 255), random.randint(0, 255)  # OEM特定
        ])
    elif payload_type == 0x0007:  # 存活检查请求
        data = b""  # 无数据
    elif payload_type == 0x4001:  # DoIP实体状态请求
        data = b""  # 无数据
    elif payload_type == 0x4003:  # 诊断电源模式信息请求
        data = b""  # 无数据
    elif payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息
        # 生成随机诊断数据
        diagnostic_services = [
            [0x10, 0x01],  # 诊断会话控制
            [0x10, 0x02],  # 编程会话
            [0x10, 0x03],  # 扩展诊断会话
            [0x11, 0x01],  # ECU复位 - 硬复位
            [0x11, 0x02],  # ECU复位 - 键开关复位
            [0x11, 0x03],  # ECU复位 - 软复位
            [0x22, 0xF1, 0x90],  # 读取数据标识符 - VIN
            [0x22, 0xF1, 0x86],  # 读取数据标识符 - ECU序列号
            [0x22, 0xF1, 0x87],  # 读取数据标识符 - 支持的功能单元
            [0x27, 0x01],  # 安全访问 - 请求种子
            [0x27, 0x02],  # 安全访问 - 发送密钥
            [0x3E, 0x00],  # 测试器存在
            [0x85, 0x01],  # 控制DTC设置 - 开启
            [0x85, 0x02],  # 控制DTC设置 - 关闭
        ]

        base_service = random.choice(diagnostic_services)
        # 添加随机数据
        extra_data = [random.randint(0, 255) for _ in range(random.randint(0, 10))]
        data = bytes(base_service + extra_data)
    else:
        # 其他类型生成随机数据
        data_length = random.randint(0, 50)
        data = bytes([random.randint(0, 255) for _ in range(data_length)])

    return payload_type, data

def main():
    global running, sock

    init()  # 初始化colorama
    setup_signal_handlers()  # 设置信号处理器

    args = parseArgs()
    target_ip = args.target
    target_port = args.port

    print(Fore.CYAN + "=" * 60)
    print(Fore.CYAN + "           DoIP 模糊测试工具")
    print(Fore.CYAN + "=" * 60)
    print(Fore.WHITE + f"目标地址: {target_ip}:{target_port}")
    print(Fore.WHITE + f"测试用例数量: {args.count}")
    print(Fore.WHITE + f"包间延迟: {args.delay}秒")
    print(Fore.YELLOW + f"提示: 按 Ctrl+C 可以安全退出程序")
    print(Fore.CYAN + "=" * 60)

    # 建立TCP连接
    sock = establish_tcp_connection(target_ip, target_port)
    if not sock:
        print(Fore.RED + "[  错误  ] 无法建立TCP连接，退出程序")
        sys.exit(1)

    # 发送路由激活消息
    if not send_routing_activation(sock):
        print(Fore.RED + "[  错误  ] 路由激活失败，但继续进行模糊测试")

    # 等待一段时间确保路由激活完成
    time.sleep(1)

    # 开始模糊测试
    print(Fore.GREEN + "\n[  开始  ] 开始DoIP模糊测试...")
    print(Fore.CYAN + "[  信息  ] 将随机发送不同类型的DoIP载荷")
    print(Fore.CYAN + f"[  信息  ] 每 {args.reconnect_interval} 个测试检查一次连接状态")
    print(Fore.CYAN + f"[  信息  ] 最大重连次数: {args.max_reconnects}")

    successful_tests = 0
    failed_tests = 0
    reconnect_count = 0

    try:
        for packet_num in range(1, args.count + 1):
            # 检查是否需要退出
            if not running:
                print(Fore.YELLOW + f"[  中断  ] 在第 {packet_num} 个测试用例时退出")
                break

            try:
                # 生成随机DoIP载荷
                payload_type, base_data = generate_random_doip_payload()

                # 对载荷数据进行模糊测试
                fuzz_data = generate_fuzz_data(base_data,
                                             random.choice(["random", "length"]))

                # 创建DoIP消息
                doip_message = create_doip_message(
                    payload_type=payload_type,
                    data=fuzz_data,
                    source_addr=random.randint(0x0E00, 0x0EFF),  # 随机源地址
                    target_addr=random.randint(0x1000, 0x17FF)   # 随机目标地址
                )

                # 显示当前测试的载荷类型
                payload_names = {
                    0x0000: "通用否定应答", 0x0001: "车辆识别请求", 0x0002: "车辆识别+EID",
                    0x0003: "车辆识别+VIN", 0x0004: "车辆公告", 0x0005: "路由激活请求",
                    0x0006: "路由激活响应", 0x0007: "存活检查请求", 0x0008: "存活检查响应",
                    0x4001: "实体状态请求", 0x4002: "实体状态响应", 0x4003: "电源模式请求",
                    0x4004: "电源模式响应", 0x8001: "诊断消息", 0x8002: "诊断正面应答",
                    0x8003: "诊断否定应答"
                }
                payload_name = payload_names.get(payload_type, f"未知类型(0x{payload_type:04X})")

                print(Fore.BLUE + f"[  {packet_num:04d}  ] 发送载荷: {payload_name} (0x{payload_type:04X}), 数据长度: {len(fuzz_data)}")

                # 定期检查连接状态
                if packet_num % args.reconnect_interval == 0:
                    if not check_connection_alive(sock):
                        print(Fore.YELLOW + f"[  {packet_num:04d}  ] 定期检查发现连接断开")
                        if reconnect_count >= args.max_reconnects:
                            print(Fore.RED + f"[  错误  ] 已达到最大重连次数 ({args.max_reconnects})，退出测试")
                            break
                        if reconnect_with_routing_activation(target_ip, target_port):
                            reconnect_count += 1
                        else:
                            print(Fore.RED + "[  错误  ] 定期重连失败，退出测试")
                            break

                # 发送模糊测试数据
                if running:  # 再次检查运行状态
                    try:
                        sock.settimeout(2)  # 设置发送超时
                        sock.send(doip_message)

                    except socket.error as e:
                        print(Fore.RED + f"[  {packet_num:04d}  ] 发送失败: {e}")

                        # 检查是否已达到最大重连次数
                        if reconnect_count >= args.max_reconnects:
                            print(Fore.RED + f"[  错误  ] 已达到最大重连次数，跳过此测试用例")
                            failed_tests += 1
                            continue

                        # 尝试重连
                        print(Fore.YELLOW + f"[  {packet_num:04d}  ] 尝试重新连接...")
                        if running and reconnect_with_routing_activation(target_ip, target_port):
                            reconnect_count += 1
                            try:
                                sock.send(doip_message)
                                print(Fore.GREEN + f"[  {packet_num:04d}  ] 重连后发送成功")
                            except socket.error as e2:
                                print(Fore.RED + f"[  {packet_num:04d}  ] 重连后仍然发送失败: {e2}")
                                failed_tests += 1
                                continue
                        else:
                            print(Fore.RED + f"[  {packet_num:04d}  ] 重连失败，跳过此测试用例")
                            failed_tests += 1
                            continue
                else:
                    break

                # 尝试接收响应
                try:
                    sock.settimeout(1.0)  # 响应超时时间
                    response = sock.recv(1024)
                    if response:
                        print(Fore.GREEN + f"[  {packet_num:04d}  ] 收到响应 ({len(response)} 字节)")
                        # 显示响应的前几个字节
                        if len(response) >= 8:
                            response_preview = " ".join([f"{b:02X}" for b in response[:8]])
                            print(Fore.CYAN + f"[  {packet_num:04d}  ] 响应预览: {response_preview}...")
                    else:
                        print(Fore.YELLOW + f"[  {packet_num:04d}  ] 收到空响应")

                except socket.timeout:
                    print(Fore.YELLOW + f"[  {packet_num:04d}  ] 响应超时")

                except socket.error as e:
                    print(Fore.RED + f"[  {packet_num:04d}  ] 接收错误: {e}")
                    # 连接可能已断开，标记为需要重连
                    print(Fore.YELLOW + f"[  {packet_num:04d}  ] 连接可能已断开，下次发送时将自动重连")
                    failed_tests += 1
                    continue

                successful_tests += 1

                # 监控目标可用性
                if args.monitor and packet_num % 10 == 0 and running:
                    if not heartBeat(target_ip, target_port):
                        print(Fore.RED + f"[  警告  ] 目标 {target_ip}:{target_port} 可能不可用")

                # 延迟（可中断的延迟）
                if running:
                    delay_start = time.time()
                    while time.time() - delay_start < args.delay and running:
                        time.sleep(0.01)  # 小间隔检查运行状态

            except KeyboardInterrupt:
                # 这个异常现在由信号处理器处理
                break
            except Exception as e:
                print(Fore.RED + f"[  {packet_num:04d}  ] 测试异常: {e}")
                failed_tests += 1
                if not running:
                    break
                continue

    except KeyboardInterrupt:
        # 额外的KeyboardInterrupt处理（以防信号处理器未捕获）
        print(Fore.YELLOW + "\n[  中断  ] 程序被用户中断")
        running = False

    finally:
        # 关闭连接
        if sock:
            try:
                sock.close()
                print(Fore.GREEN + "[  TCP  ] 连接已安全关闭")
            except:
                pass

        # 打印统计信息
        print(Fore.CYAN + "\n" + "=" * 70)
        if running:
            print(Fore.CYAN + "                测试完成统计")
        else:
            print(Fore.CYAN + "                测试中断统计")
        print(Fore.CYAN + "=" * 70)
        print(Fore.GREEN + f"成功测试: {successful_tests}")
        print(Fore.RED + f"失败测试: {failed_tests}")
        print(Fore.WHITE + f"总计测试: {successful_tests + failed_tests}")
        print(Fore.YELLOW + f"重连次数: {reconnect_count}")

        # 计算连接稳定性
        total_tests = successful_tests + failed_tests
        if total_tests > 0:
            success_rate = (successful_tests / total_tests) * 100
            print(Fore.CYAN + f"成功率: {success_rate:.1f}%")

            if reconnect_count > 0:
                avg_tests_per_reconnect = total_tests / reconnect_count
                print(Fore.CYAN + f"连接稳定性: 平均每 {avg_tests_per_reconnect:.1f} 个测试重连一次")
            else:
                print(Fore.GREEN + f"连接稳定性: 整个测试过程无需重连")

        if not running:
            print(Fore.YELLOW + f"状态: 用户中断退出")
        else:
            print(Fore.GREEN + f"状态: 正常完成")

        print(Fore.CYAN + "=" * 70)
        print(Fore.WHITE + "感谢使用DoIP模糊测试工具！")

if __name__ == "__main__":
    main()
