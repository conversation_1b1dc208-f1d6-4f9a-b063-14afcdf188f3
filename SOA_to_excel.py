from docx import Document
import pandas as pd
import re
from openpyxl import Workbook

doc = Document('SOA安全管理平台测试报告.docx')

par_data = []

wb = Workbook()
ws = wb.active

for each in doc.tables:
    row_data = []
    row_data.append(each.cell(0,1).text)
    row_data.append(each.cell(1,1).text)
    row_data.append('Pass')
    row_data.append('详见测试记录\n'+ each.cell(0,1).text)
  
    ws.append(row_data)

wb.save('SOA安全管理平台测试.xlsx')