import os
import zipfile
import chardet
import glob
import gzip
import shutil
import threading
import signal
import sys
import time
from typing import List
from progress.bar import Bar
from progress.spinner import Spinner
import re  # 添加正则表达式模块

# 在文件开头添加全局变量
found_results = []
results_lock = threading.Lock()
progress_bars = {}
progress_lock = threading.Lock()
stop_search = False
active_threads = set()
threads_lock = threading.Lock()

def create_progress_bar(thread_name: str, total: int) -> Bar:
    """为线程创建进度条"""
    with progress_lock:
        progress_bars[thread_name] = Bar(f'{thread_name}', max=total)
        return progress_bars[thread_name]

def update_progress(thread_name: str):
    """更新线程的进度条"""
    with progress_lock:
        if thread_name in progress_bars:
            progress_bars[thread_name].next()

def finish_progress(thread_name: str):
    """完成进度条"""
    with progress_lock:
        if thread_name in progress_bars:
            progress_bars[thread_name].finish()

def signal_handler(signum, frame):
    """处理 Ctrl+C 信号"""
    global stop_search
    print("\n\n收到终止信号，正在停止搜索...")
    stop_search = True
    
    # 等待所有活动线程完成
    while active_threads:
        thread_name = next(iter(active_threads))
        print(f"等待 {thread_name} 终止...")
        time.sleep(0.5)
    
    print("\n搜索已终止！")
    sys.exit(0)

def is_regex(pattern: str) -> bool:
    """判断是否为正则表达式"""
    # 包含正则表达式特殊字符的字符串被视为正则表达式
    regex_chars = '.^$*+?{}[]\\|()'
    return any(c in pattern for c in regex_chars)

def search_in_line(pattern: str, line: str, ignore_case: bool = True) -> bool:
    """搜索一行文本，支持普通字符串和正则表达式"""
    try:
        if is_regex(pattern):
            # 使用正则表达式搜索
            flags = re.IGNORECASE if ignore_case else 0
            return bool(re.search(pattern, line, flags))
        else:
            # 使用普通字符串搜索
            if ignore_case:
                return pattern.lower() in line.lower()
            return pattern in line
    except re.error:
        # 如果正则表达式无效，回退到普通字符串搜索
        if ignore_case:
            return pattern.lower() in line.lower()
        return pattern in line

def search_files(gz_files: List[str], search_strings: List[str], thread_name: str):
    """处理一组文件的搜索任务"""
    global stop_search
    
    # 注册线程为活动状态
    with threads_lock:
        active_threads.add(thread_name)
    
    try:
        # 创建进度条
        progress_bar = create_progress_bar(thread_name, len(gz_files))
        
        # 为每个线程创建输出文件
        output_file = f"{thread_name}_results.txt"
        print(f"{thread_name} 开始处理，负责 {len(gz_files)} 个文件")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入搜索信息头
            f.write(f"{thread_name} 搜索结果\n")
            f.write(f"搜索关键词: {', '.join(search_strings)}\n")
            f.write(f"处理文件数: {len(gz_files)}\n")
            f.write("-" * 50 + "\n\n")
            
            for gz_file in gz_files:
                # 检查是否收到终止信号
                if stop_search:
                    f.write("\n搜索被用户终止\n")
                    break
                
                try:
                    # 重定向输出到文件
                    found = False
                    local_results = {s.lower(): [] for s in search_strings}
                    
                    try:
                        with gzip.open(gz_file, 'rt', encoding='utf-8') as gz_file_content:
                            for line_num, line in enumerate(gz_file_content, 1):
                                for search_string in search_strings:
                                    if search_in_line(search_string, line):
                                        if not found:
                                            f.write(f"\n在压缩包中找到匹配: {gz_file}\n")
                                            found = True
                                        # 对于正则表达式，显示模式信息
                                        pattern_type = "正则" if is_regex(search_string) else "字符串"
                                        f.write(f"    [{pattern_type}:{search_string}] {line.strip()}\n")
                                        local_results[search_string.lower()].append(line.strip())
                                        
                    except UnicodeDecodeError:
                        try:
                            with gzip.open(gz_file, 'rb') as gz_file_content:
                                content = gz_file_content.read()
                                encoding = chardet.detect(content)['encoding'] or 'utf-8'
                                
                                for line_num, line in enumerate(content.decode(encoding).splitlines(), 1):
                                    for search_string in search_strings:
                                        if search_in_line(search_string, line):
                                            if not found:
                                                f.write(f"\n在压缩包中找到匹配: {gz_file}\n")
                                                found = True
                                            pattern_type = "正则" if is_regex(search_string) else "字符串"
                                            f.write(f"    [{pattern_type}:{search_string}] {line.strip()}\n")
                                            local_results[search_string.lower()].append(line.strip())
                                        
                        except Exception as e:
                            f.write(f"处理文件 {gz_file} 时出错: {str(e)}\n")
                    
                    # 如果找到了匹配项，添加到全局结果
                    if found:
                        with results_lock:
                            found_results.append({
                                'file': gz_file,
                                'matches': local_results
                            })
                    else:
                        search_strings_str = "', '".join(search_strings)
                        f.write(f"\n{gz_file} 搜索完毕，无 '{search_strings_str}' 字符串\n")
                    
                except Exception as e:
                    f.write(f"处理文件 {gz_file} 时出错: {str(e)}\n")
                
                # 更新进度条
                update_progress(thread_name)
            
            # 写入搜索完成信息
            f.write("\n" + "-" * 50)
            f.write(f"\n{thread_name} {'搜索完成' if not stop_search else '搜索被终止'}！\n")
        
    finally:
        # 完成进度条
        finish_progress(thread_name)
        # 从活动线程集合中移除
        with threads_lock:
            active_threads.remove(thread_name)

def init_progress_bars(thread_count: int, files_per_thread: List[int]) -> None:
    """初始化所有线程的进度条"""
    print("\n初始化进度条...")
    for i in range(thread_count):
        thread_name = f"线程{i+1}"
        progress_bars[thread_name] = Bar(f'{thread_name}', max=files_per_thread[i])
    print("进度条初始化完成\n")

def main():
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    import argparse
    parser = argparse.ArgumentParser(description='在GZ压缩的日志文件中搜索多个字符串')
    parser.add_argument('search_strings', nargs='+', help='要搜索的字符串或正则表达式（需要用双引号包围）')
    parser.add_argument('--path', default='.', help='要搜索的目录路径，默认为当前目录')
    
    args = parser.parse_args()
    
    # 清空之前的搜索结果和状态
    found_results.clear()
    progress_bars.clear()
    global stop_search
    stop_search = False
    active_threads.clear()
    
    # 获取指定目录下的所有gz文件
    gz_files = glob.glob(os.path.join(args.path, "*.gz"))
    
    if not gz_files:
        print(f"错误: 在目录 {args.path} 中没有找到GZ文件")
        return
    
    print(f"开始搜索:")
    for pattern in args.search_strings:
        pattern_type = "正则表达式" if is_regex(pattern) else "字符串"
        print(f"  - {pattern_type}: {pattern}")
    print(f"共找到 {len(gz_files)} 个gz文件")
    print("-" * 50)
    
    # 将文件列表分成三份
    file_count = len(gz_files)
    part_size = file_count // 3
    
    # 分配文件给三个线程
    files_part1 = gz_files[:part_size]
    files_part2 = gz_files[part_size:2*part_size]
    files_part3 = gz_files[2*part_size:]
    
    # 初始化所有进度条
    thread_files = [len(files_part1), len(files_part2), len(files_part3)]
    init_progress_bars(3, thread_files)
    
    print("搜索进度：")
    
    # 创建三个线程
    thread1 = threading.Thread(
        target=search_files,
        args=(files_part1, args.search_strings, "线程1"),
        name="SearchThread1"
    )
    
    thread2 = threading.Thread(
        target=search_files,
        args=(files_part2, args.search_strings, "线程2"),
        name="SearchThread2"
    )
    
    thread3 = threading.Thread(
        target=search_files,
        args=(files_part3, args.search_strings, "线程3"),
        name="SearchThread3"
    )
    
    # 启动所有线程
    thread1.start()
    thread2.start()
    thread3.start()
    
    try:
        # 等待所有线程完成
        thread1.join()
        thread2.join()
        thread3.join()
        
        if not stop_search:
            # 只有在正常完成时才打印汇总结果
            print("\n" + "="*50)
            print("搜索结果汇总:")
            print("="*50)
            print("\n各线程的搜索结果已保存到以下文件：")
            print("线程1: 线程1_results.txt")
            print("线程2: 线程2_results.txt")
            print("线程3: 线程3_results.txt")
            
            if found_results:
                print("\n找到匹配的文件汇总：")
                for result in sorted(found_results, key=lambda x: x['file']):
                    print(f"\n文件: {result['file']}")
                    for search_string in args.search_strings:
                        matches = result['matches'][search_string.lower()]
                        if matches:
                            print(f"  - 包含关键词 '{search_string}' 的匹配行数: {len(matches)}")
            else:
                print("\n没有找到任何匹配项")
            
            print("\n搜索完成！")
    except KeyboardInterrupt:
        # 如果在等待过程中收到 Ctrl+C，让信号处理器处理
        pass

if __name__ == "__main__":
    main()
