#!/usr/bin/env python3
"""
TPMS程序测试脚本
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"测试: {description}")
    print(f"命令: {cmd}")
    print('='*50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("命令执行超时")
        return False
    except Exception as e:
        print(f"执行错误: {e}")
        return False

def main():
    print("TPMS程序功能测试")
    print("="*60)
    
    tests = [
        ("python tpms.py --mode analyze", "CRC分析模式"),
        ("python tpms.py --mode modify --pressure 50 --temperature 25", "数据修改模式"),
        ("python tpms.py --mode simulate", "信号模拟模式 - 原始数据"),
        ("python tpms.py --mode simulate --pressure 100 --temperature 30", "信号模拟模式 - 修改数据"),
        ("python tpms.py --mode simulate --sensor-id 12345678", "信号模拟模式 - 修改传感器ID"),
        ("python tpms.py --mode simulate --data 00000000014d0414de0104a6ff2a06", "信号模拟模式 - 自定义数据"),
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for cmd, description in tests:
        if run_command(cmd, description):
            success_count += 1
            print("✓ 测试通过")
        else:
            print("✗ 测试失败")
    
    print(f"\n{'='*60}")
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了!")
    else:
        print("⚠️  部分测试失败，请检查程序")
    
    # 检查生成的文件
    print(f"\n{'='*60}")
    print("检查生成的文件:")
    
    files_to_check = ["tpms_signal.bin"]
    for filename in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✓ {filename} - {size} bytes")
        else:
            print(f"✗ {filename} - 文件不存在")

if __name__ == "__main__":
    main()
