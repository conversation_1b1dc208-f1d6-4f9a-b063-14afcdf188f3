import serial
import time
import threading
import queue
from datetime import datetime
import logging

# 配置串口参数
SERIAL_PORT = 'COM3'
BAUDRATE = 115200
TIMEOUT = 0.1  # 减少超时时间以提高响应性

# 要发送的数据（可以是 bytes 或 ASCII）
SEND_DATA = b'\x01\x02\x03\x04'  # 示例：发送4字节数据
# 或者发送 ASCII 字符串，如：
# SEND_DATA = "Hello UART\n".encode()

# 空闲检测配置
IDLE_TIMEOUT = 0.5  # 空闲超时时间（秒）
SNIFF_BUFFER_SIZE = 1024  # 嗅探缓冲区大小

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uart_sniff.log'),
        logging.StreamHandler()
    ]
)

class UARTSniffer:
    """UART数据嗅探器，支持空闲检测和自动发送"""

    def __init__(self, port=SERIAL_PORT, baudrate=BAUDRATE,
                 idle_timeout=IDLE_TIMEOUT, send_data=SEND_DATA):
        self.port = port
        self.baudrate = baudrate
        self.idle_timeout = idle_timeout
        self.send_data = send_data

        self.serial_conn = None
        self.is_running = False
        self.last_data_time = None
        self.data_queue = queue.Queue()

        # 线程控制
        self.sniff_thread = None
        self.idle_monitor_thread = None
        self.stop_event = threading.Event()

        self.logger = logging.getLogger(__name__)

    def connect(self):
        """连接串口"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=TIMEOUT,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                bytesize=serial.EIGHTBITS
            )

            if self.serial_conn.is_open:
                self.logger.info(f"串口 {self.port} 连接成功")
                return True
            else:
                self.logger.error(f"无法打开串口 {self.port}")
                return False

        except serial.SerialException as e:
            self.logger.error(f"串口连接错误: {e}")
            return False

    def disconnect(self):
        """断开串口连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            self.logger.info("串口已断开")

    def _sniff_data(self):
        """数据嗅探线程"""
        self.logger.info("开始嗅探UART数据...")

        while not self.stop_event.is_set():
            try:
                if self.serial_conn.in_waiting > 0:
                    data = self.serial_conn.read(self.serial_conn.in_waiting)
                    if data:
                        current_time = time.time()
                        self.last_data_time = current_time

                        # 记录接收到的数据
                        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                        self.logger.info(f"[{timestamp}] RX: {data.hex()} ({len(data)} bytes)")

                        # 将数据放入队列供其他处理使用
                        self.data_queue.put((current_time, data))

                time.sleep(0.001)  # 短暂休眠避免CPU占用过高

            except serial.SerialException as e:
                self.logger.error(f"数据嗅探错误: {e}")
                break
            except Exception as e:
                self.logger.error(f"嗅探线程异常: {e}")
                break

    def _monitor_idle(self):
        """空闲监控线程"""
        self.logger.info("开始监控UART空闲状态...")

        while not self.stop_event.is_set():
            try:
                current_time = time.time()

                # 检查是否达到空闲条件
                if (self.last_data_time is not None and
                    current_time - self.last_data_time >= self.idle_timeout):

                    # 检测到空闲，发送数据
                    self._send_on_idle()

                    # 重置最后数据时间，避免重复发送
                    self.last_data_time = current_time

                time.sleep(0.01)  # 10ms检查间隔

            except Exception as e:
                self.logger.error(f"空闲监控线程异常: {e}")
                break

    def _send_on_idle(self):
        """在检测到空闲时发送数据"""
        try:
            if self.serial_conn and self.serial_conn.is_open:
                self.serial_conn.write(self.send_data)
                timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                self.logger.info(f"[{timestamp}] TX (空闲触发): {self.send_data.hex()} ({len(self.send_data)} bytes)")

        except serial.SerialException as e:
            self.logger.error(f"发送数据错误: {e}")
        except Exception as e:
            self.logger.error(f"发送数据异常: {e}")

    def start_sniffing(self):
        """开始嗅探"""
        if not self.connect():
            return False

        self.is_running = True
        self.stop_event.clear()
        self.last_data_time = time.time()  # 初始化时间

        # 启动嗅探线程
        self.sniff_thread = threading.Thread(target=self._sniff_data, daemon=True)
        self.sniff_thread.start()

        # 启动空闲监控线程
        self.idle_monitor_thread = threading.Thread(target=self._monitor_idle, daemon=True)
        self.idle_monitor_thread.start()

        self.logger.info("UART嗅探器已启动")
        return True

    def stop_sniffing(self):
        """停止嗅探"""
        self.logger.info("正在停止UART嗅探器...")

        self.is_running = False
        self.stop_event.set()

        # 等待线程结束
        if self.sniff_thread and self.sniff_thread.is_alive():
            self.sniff_thread.join(timeout=1.0)

        if self.idle_monitor_thread and self.idle_monitor_thread.is_alive():
            self.idle_monitor_thread.join(timeout=1.0)

        self.disconnect()
        self.logger.info("UART嗅探器已停止")

    def send_manual_data(self, data=None):
        """手动发送数据"""
        if data is None:
            data = self.send_data

        try:
            if self.serial_conn and self.serial_conn.is_open:
                self.serial_conn.write(data)
                timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                self.logger.info(f"[{timestamp}] TX (手动): {data.hex()} ({len(data)} bytes)")
                return True
            else:
                self.logger.error("串口未连接，无法发送数据")
                return False

        except Exception as e:
            self.logger.error(f"手动发送数据错误: {e}")
            return False


def uart_send_receive():
    """原始的发送接收函数（保持向后兼容）"""
    try:
        # 打开串口
        ser = serial.Serial(
            port=SERIAL_PORT,
            baudrate=BAUDRATE,
            timeout=1,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            bytesize=serial.EIGHTBITS
        )

        if ser.is_open:
            print(f"[INFO] 串口 {SERIAL_PORT} 已打开，准备发送数据...")

            # 发送数据
            ser.write(SEND_DATA)
            print(f"[TX] 发送数据: {SEND_DATA}")

            # 等待设备响应
            time.sleep(0.1)  # 视情况延迟

            # 接收回显/响应
            response = ser.read(64)  # 最多读取64字节
            print(f"[RX] 接收到数据: {response}")

            ser.close()
        else:
            print(f"[ERROR] 无法打开串口 {SERIAL_PORT}")

    except serial.SerialException as e:
        print(f"[EXCEPTION] 串口错误: {e}")


def uart_sniff_test():
    """UART嗅探测试函数"""
    print("=== UART数据嗅探和空闲检测测试 ===")
    print(f"串口: {SERIAL_PORT}")
    print(f"波特率: {BAUDRATE}")
    print(f"空闲超时: {IDLE_TIMEOUT}秒")
    print(f"发送数据: {SEND_DATA.hex()}")
    print("按 Ctrl+C 停止嗅探\n")

    # 创建嗅探器实例
    sniffer = UARTSniffer()

    try:
        # 开始嗅探
        if sniffer.start_sniffing():
            print("嗅探器已启动，正在监控UART通道...")

            # 主循环
            while True:
                time.sleep(1)

                # 可以在这里添加其他逻辑
                # 例如：检查队列中的数据，进行额外处理等

        else:
            print("无法启动嗅探器")

    except KeyboardInterrupt:
        print("\n收到停止信号...")
    finally:
        sniffer.stop_sniffing()
        print("测试结束")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "sniff":
        # 运行嗅探测试
        uart_sniff_test()
    else:
        # 运行原始的发送接收测试
        print("运行原始UART发送接收测试...")
        print("使用 'python uart.py sniff' 来运行嗅探测试")
        uart_send_receive()
