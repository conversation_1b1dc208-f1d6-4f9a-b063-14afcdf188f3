from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import serialization
import datetime
from cryptography import x509
from cryptography.hazmat.primitives import hashes
from cryptography.x509.oid import NameOID

# Generate ECC 384 private key
private_key = ec.generate_private_key(ec.SECP384R1())

# Get the public key from the private key
public_key = private_key.public_key()

# Serialize the private key and public key to PEM format
pem_private_key = private_key.private_bytes(
    encoding=serialization.Encoding.PEM,
    format=serialization.PrivateFormat.PKCS8,
    encryption_algorithm=serialization.NoEncryption()
)
pem_public_key = public_key.public_bytes(
    encoding=serialization.Encoding.PEM,
    format=serialization.PublicFormat.SubjectPublicKeyInfo
)

# Create a self-signed certificate using the public key as the root certificate
builder = x509.CertificateBuilder()
builder = builder.subject_name(x509.Name([
    x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
    x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
    x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
    x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Example Inc."),
    x509.NameAttribute(NameOID.COMMON_NAME, "Example Root Certificate"),
]))
builder = builder.issuer_name(x509.Name([
    x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
    x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
    x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
    x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Example Inc."),
    x509.NameAttribute(NameOID.COMMON_NAME, "Example Root Certificate"),
]))
builder = builder.public_key(public_key)
builder = builder.serial_number(x509.random_serial_number())
builder = builder.not_valid_before(datetime.datetime.utcnow())
builder = builder.not_valid_after(datetime.datetime.utcnow() + datetime.timedelta(days=365))
builder = builder.add_extension(x509.BasicConstraints(ca=True, path_length=None), critical=True)
root_cert = builder.sign(private_key=private_key, algorithm=hashes.SHA256())

# Serialize the root certificate to PEM format
pem_root_cert = root_cert.public_bytes(encoding=serialization.Encoding.PEM)

# Load the ECC 256 public key
ecc256_public_key_pem = """
-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkNGkk+veyNiXxJP4tA6beS7SH/cqEZ/yMiwJBHPSrXxuk1lqFdEL0VV3maCowT7R9QKrEPQN6CzIKVgCfhdefA==        
-----END PUBLIC KEY-----
"""
ecc256_public_key = serialization.load_pem_public_key(ecc256_public_key_pem.encode())

# Create a certificate for the ECC 256 public key signed by the root certificate
builder = x509.CertificateBuilder()
builder = builder.subject_name(x509.Name([
    x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
    x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
    x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
    x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Example Inc."),
    x509.NameAttribute(NameOID.COMMON_NAME, "Example ECC 256 Certificate"),
]))
builder = builder.issuer_name(root_cert.subject)
builder = builder.public_key(ecc256_public_key)
builder = builder.serial_number(x509.random_serial_number())
builder = builder.not_valid_before(datetime.datetime.utcnow())
builder = builder.not_valid_after(datetime.datetime.utcnow() + datetime.timedelta(days=365))
builder = builder.add_extension(x509.BasicConstraints(ca=False, path_length=None), critical=True)
signed_cert = builder.sign(private_key=private_key, algorithm=hashes.SHA256())

# Serialize the signed certificate to PEM format
pem_signed_cert = signed_cert.public_bytes(encoding=serialization.Encoding.PEM)

# Print the root certificate and the signed certificate
print("Root Certificate:\n", pem_root_cert.decode())
print("Signed Certificate:\n", pem_signed_cert.decode())