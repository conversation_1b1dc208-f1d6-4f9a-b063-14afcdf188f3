#!/usr/bin/env python3
"""
TPMS批量信号管理器
用于管理暴力破解生成的多个信号文件
"""

import os
import glob
import argparse
import subprocess
import time

def list_crc_files():
    """列出所有CRC暴力破解生成的文件"""
    pattern = "tpms_crc_*.bin"
    files = glob.glob(pattern)
    files.sort()
    
    print(f"找到 {len(files)} 个CRC暴力破解文件:")
    for i, filename in enumerate(files, 1):
        size = os.path.getsize(filename)
        crc_value = filename.split('_')[-1].split('.')[0]
        print(f"  {i:2d}. {filename} (CRC: 0x{crc_value}, 大小: {size} bytes)")
    
    return files

def transmit_with_hackrf(filename, freq=433.92e6, gain=47, sample_rate=2e6):
    """使用HackRF发送单个文件"""
    print(f"使用HackRF发送: {filename}")
    
    # 构建hackrf_transfer命令
    cmd = [
        "hackrf_transfer",
        "-t", filename,
        "-f", str(int(freq)),
        "-a", str(gain),
        "-s", str(int(sample_rate)),
        "-x", "47"  # TX VGA gain
    ]
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ 发送成功")
            return True
        else:
            print(f"✗ 发送失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 发送超时")
        return False
    except FileNotFoundError:
        print("✗ hackrf_transfer命令未找到，请确保HackRF工具已安装")
        return False
    except Exception as e:
        print(f"✗ 发送错误: {e}")
        return False

def batch_transmit(files, interval=1.0, freq=433.92e6, repeat_each=1):
    """批量发送文件"""
    print(f"开始批量发送 {len(files)} 个文件")
    print(f"发送间隔: {interval}秒")
    print(f"每个文件重复: {repeat_each}次")
    print(f"载波频率: {freq/1e6:.2f} MHz")
    print("="*50)
    
    success_count = 0
    
    for i, filename in enumerate(files, 1):
        crc_value = filename.split('_')[-1].split('.')[0]
        print(f"\n[{i}/{len(files)}] CRC 0x{crc_value}: {filename}")
        
        for repeat in range(repeat_each):
            if repeat_each > 1:
                print(f"  重复 {repeat+1}/{repeat_each}")
            
            if transmit_with_hackrf(filename, freq):
                success_count += 1
            
            # 重复间隔
            if repeat < repeat_each - 1:
                time.sleep(0.5)
        
        # 文件间隔
        if i < len(files):
            print(f"等待 {interval}秒...")
            time.sleep(interval)
    
    print("="*50)
    print(f"批量发送完成! 成功: {success_count}/{len(files) * repeat_each}")

def cleanup_files(pattern="tpms_crc_*.bin"):
    """清理生成的文件"""
    files = glob.glob(pattern)
    
    if not files:
        print("没有找到要清理的文件")
        return
    
    print(f"找到 {len(files)} 个文件:")
    for filename in files:
        print(f"  {filename}")
    
    confirm = input(f"确认删除这些文件? (y/N): ")
    if confirm.lower() == 'y':
        for filename in files:
            try:
                os.remove(filename)
                print(f"✓ 已删除: {filename}")
            except Exception as e:
                print(f"✗ 删除失败 {filename}: {e}")
    else:
        print("已取消")

def generate_gnu_radio_playlist(files, output_file="tpms_playlist.txt"):
    """生成GNU Radio播放列表"""
    with open(output_file, 'w') as f:
        for filename in files:
            abs_path = os.path.abspath(filename)
            f.write(f"{abs_path}\n")
    
    print(f"GNU Radio播放列表已生成: {output_file}")
    print("可以在GNU Radio中使用File Source块循环播放这些文件")

def analyze_crc_coverage(files):
    """分析CRC覆盖范围"""
    crc_values = []
    
    for filename in files:
        try:
            crc_hex = filename.split('_')[-1].split('.')[0]
            crc_val = int(crc_hex, 16)
            crc_values.append(crc_val)
        except ValueError:
            continue
    
    if not crc_values:
        print("没有找到有效的CRC文件")
        return
    
    crc_values.sort()
    
    print("CRC覆盖分析:")
    print(f"  总文件数: {len(crc_values)}")
    print(f"  CRC范围: 0x{min(crc_values):02x} - 0x{max(crc_values):02x}")
    print(f"  覆盖率: {len(crc_values)}/256 ({len(crc_values)/256*100:.1f}%)")
    
    # 检查缺失的CRC值
    missing = []
    for i in range(min(crc_values), max(crc_values) + 1):
        if i not in crc_values:
            missing.append(i)
    
    if missing:
        print(f"  缺失的CRC值: {len(missing)} 个")
        if len(missing) <= 10:
            missing_hex = [f"0x{x:02x}" for x in missing]
            print(f"    {', '.join(missing_hex)}")
        else:
            print(f"    0x{missing[0]:02x} - 0x{missing[-1]:02x} (等)")

def main():
    parser = argparse.ArgumentParser(description='TPMS批量信号管理器')
    parser.add_argument('--action', choices=['list', 'transmit', 'cleanup', 'playlist', 'analyze'], 
                       default='list', help='操作类型')
    parser.add_argument('--interval', type=float, default=1.0, help='发送间隔(秒)')
    parser.add_argument('--freq', type=float, default=433.92e6, help='载波频率(Hz)')
    parser.add_argument('--repeat', type=int, default=1, help='每个文件重复次数')
    parser.add_argument('--pattern', default='tpms_crc_*.bin', help='文件匹配模式')
    
    args = parser.parse_args()
    
    print("=== TPMS批量信号管理器 ===")
    
    if args.action == 'list':
        list_crc_files()
    
    elif args.action == 'transmit':
        files = list_crc_files()
        if files:
            confirm = input(f"\n确认发送 {len(files)} 个文件? (y/N): ")
            if confirm.lower() == 'y':
                batch_transmit(files, args.interval, args.freq, args.repeat)
            else:
                print("已取消")
        else:
            print("没有找到CRC文件")
    
    elif args.action == 'cleanup':
        cleanup_files(args.pattern)
    
    elif args.action == 'playlist':
        files = glob.glob(args.pattern)
        if files:
            generate_gnu_radio_playlist(files)
        else:
            print("没有找到匹配的文件")
    
    elif args.action == 'analyze':
        files = glob.glob(args.pattern)
        if files:
            analyze_crc_coverage(files)
        else:
            print("没有找到匹配的文件")

if __name__ == "__main__":
    main()
