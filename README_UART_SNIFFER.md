# UART数据嗅探器

一个功能完整的UART数据嗅探和空闲检测系统，支持实时监控UART通道并在检测到空闲时自动发送数据。

## 功能特性

- 🔍 **实时数据嗅探**: 持续监控UART通道上的数据传输
- ⏱️ **智能空闲检测**: 可配置的空闲超时检测机制
- 🚀 **自动数据发送**: 检测到空闲时立即发送预定义数据
- 📝 **详细日志记录**: 完整的时间戳和十六进制数据记录
- 🧵 **多线程架构**: 并发处理数据嗅探和空闲监控
- ⚙️ **灵活配置**: 支持多种预设配置和自定义参数
- 🧪 **完整测试**: 包含单元测试和集成测试

## 文件结构

```
├── uart.py                 # 核心UART嗅探器类
├── uart_sniff_demo.py      # 演示脚本
├── uart_config.py          # 配置管理工具
├── test_uart_sniffer.py    # 测试脚本
└── README_UART_SNIFFER.md  # 说明文档
```

## 快速开始

### 1. 安装依赖

```bash
pip install pyserial
```

### 2. 基本使用

#### 方式一：使用原有接口（向后兼容）
```bash
python uart.py
```

#### 方式二：使用新的嗅探功能
```bash
python uart.py sniff
```

#### 方式三：使用演示脚本
```bash
python uart_sniff_demo.py interactive
```

### 3. 编程接口

```python
from uart import UARTSniffer

# 创建嗅探器实例
sniffer = UARTSniffer(
    port='COM3',
    baudrate=115200,
    idle_timeout=0.5,
    send_data=b'\x01\x02\x03\x04'
)

# 启动嗅探
if sniffer.start_sniffing():
    print("嗅探器已启动")
    
    # 运行一段时间
    time.sleep(10)
    
    # 手动发送数据
    sniffer.send_manual_data(b'\xAA\xBB\xCC\xDD')
    
    # 停止嗅探
    sniffer.stop_sniffing()
```

## 配置选项

### 基本配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `port` | str | 'COM3' | 串口号 |
| `baudrate` | int | 115200 | 波特率 |
| `idle_timeout` | float | 0.5 | 空闲超时时间（秒） |
| `send_data` | bytes | b'\x01\x02\x03\x04' | 空闲时发送的数据 |

### 预设配置

使用配置工具查看和选择预设：

```bash
python uart_config.py
```

可用预设：
- `debug`: 快速响应，详细日志
- `production`: 高速稳定配置
- `slow_device`: 适用于慢速设备
- `high_speed`: 高速响应配置

## 使用示例

### 1. 交互式演示

```bash
python uart_sniff_demo.py interactive
```

提供完整的交互式配置界面，可以：
- 选择串口和波特率
- 配置空闲超时时间
- 自定义发送数据
- 实时控制发送

### 2. 自动演示

```bash
python uart_sniff_demo.py auto
```

运行30秒自动演示，展示：
- 自动空闲检测
- 定时手动发送
- 完整的日志输出

### 3. 简单嗅探

```bash
python uart_sniff_demo.py simple
```

使用默认配置进行简单嗅探。

## 工作原理

### 空闲检测算法

1. **数据监控**: 持续监控串口接收缓冲区
2. **时间戳记录**: 记录每次接收到数据的时间
3. **空闲判断**: 当前时间与最后数据时间差超过设定阈值
4. **自动发送**: 检测到空闲立即发送预定义数据
5. **防重复**: 发送后重置时间戳避免重复发送

### 多线程架构

- **嗅探线程**: 负责持续监控和接收数据
- **监控线程**: 负责空闲检测和自动发送
- **主线程**: 负责用户交互和控制

## 测试

### 运行所有测试

```bash
python test_uart_sniffer.py
```

### 运行特定测试

```bash
# 单元测试
python test_uart_sniffer.py unit

# 模拟测试
python test_uart_sniffer.py mock

# 性能测试
python test_uart_sniffer.py perf
```

## 日志输出

系统会生成详细的日志，包括：

```
2024-01-01 12:00:00,123 - INFO - 串口 COM3 连接成功
2024-01-01 12:00:00,124 - INFO - 开始嗅探UART数据...
2024-01-01 12:00:00,125 - INFO - 开始监控UART空闲状态...
2024-01-01 12:00:01,234 - INFO - [12:00:01.234] RX: 48656c6c6f (5 bytes)
2024-01-01 12:00:02,000 - INFO - [12:00:02.000] TX (空闲触发): 01020304 (4 bytes)
```

## 故障排除

### 常见问题

1. **串口无法打开**
   - 检查串口号是否正确
   - 确认串口未被其他程序占用
   - 验证用户权限

2. **数据接收异常**
   - 检查波特率设置
   - 确认数据位、停止位、校验位配置
   - 检查硬件连接

3. **空闲检测不工作**
   - 调整空闲超时时间
   - 检查是否有持续的数据流
   - 验证时间戳更新逻辑

### 调试技巧

1. **启用详细日志**:
   ```python
   import logging
   logging.getLogger().setLevel(logging.DEBUG)
   ```

2. **使用模拟测试**:
   ```bash
   python test_uart_sniffer.py mock
   ```

3. **检查串口状态**:
   ```bash
   python uart_config.py
   # 选择 "3. 列出可用串口"
   ```

## 扩展功能

### 自定义数据处理

```python
class CustomSniffer(UARTSniffer):
    def _process_received_data(self, data):
        # 自定义数据处理逻辑
        if data.startswith(b'\xAA'):
            self.logger.info("检测到特殊数据包")
        return data
```

### 条件发送

```python
def conditional_send(sniffer, condition_func):
    """根据条件发送数据"""
    while not sniffer.stop_event.is_set():
        if condition_func():
            sniffer.send_manual_data()
        time.sleep(0.1)
```

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
