import sys
import subprocess
import shutil
import cmd2
def find_python_executable():
    versions = ['python','python3', 'python3.9','python3.8', 'python3.7', 'python3.6','python39', 'python38', 'python37', 'python36']
    for version in versions:
        if shutil.which(version): 
            return version
    return None
        
class ScriptExecutor(cmd2.Cmd):
    """基于cmd2的命令行应用，执行脚本"""
    prompt = 'executor> '
    # 'execute'命令：用来执行指定的脚本
    def do_execute(self, args):
        """执行指定的Python脚本"""
        args = args.split(' ')
        if len(args) < 2 or args[0] != '--script':
            self.poutput("Usage: execute --script my_script.py [script_args]")
            return
        script_path = args[1]
        script_args = args[2:]  # 获取传递给脚本的其他参数
        python_executable = find_python_executable()
        try:
            # 执行指定的脚本，并传递参数
            result = subprocess.run(
                [python_executable, script_path] + script_args,
                check=True,
                capture_output=True,
                text=True
            )
            # 输出脚本返回的标准输出
            self.poutput(f"Output:\n{result.stdout}")
        except subprocess.CalledProcessError as e:
            self.poutput(f"Error executing {script_path}: {e}")
            self.poutput(f"Output:\n{e.stdout}")  # 脚本的标准输出
            self.poutput(f"Error:\n{e.stderr}")    # 脚本的标准错误
if __name__ == '__main__':
    # 创建ScriptExecutor对象并运行cmd2应用
    app = ScriptExecutor()
    app.cmdloop()