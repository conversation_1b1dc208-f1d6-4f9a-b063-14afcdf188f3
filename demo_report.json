{"apk_file": "demo_sm_crypto.apk", "analysis_summary": {"total_candidates": 2, "filtered_candidates": 2, "verified_algorithms": 2}, "algorithms_found": {"SM4": {"count": 2, "types": ["native_constant"], "confidence_levels": ["high", "high"]}}, "detailed_results": [{"type": "native_constant", "file": "lib/arm64-v8a/libsmcrypto.so", "section": "raw_binary", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": "raw_binary_search", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在raw_binary段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libopenssl_sm.so", "section": "raw_binary", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": "raw_binary_search", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在raw_binary段匹配到算法常量，可能为OpenSSL库变体", "openssl_variant": true}}]}