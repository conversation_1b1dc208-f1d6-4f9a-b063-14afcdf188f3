#!/usr/bin/env python3
"""
简单的国密算法检测测试
直接测试常量匹配功能
"""

import sys
import os
import tempfile
import zipfile
import binascii

# 导入我们的模块
import importlib.util
spec = importlib.util.spec_from_file_location("sm_detect", "SM detect.py")
sm_detect = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sm_detect)

APKAnalyzer = sm_detect.APKAnalyzer
SMCryptoConstants = sm_detect.SMCryptoConstants


def create_simple_test_apk():
    """创建一个简单的测试APK，包含明显的国密算法特征"""
    temp_apk = tempfile.NamedTemporaryFile(suffix='.apk', delete=False)
    constants = SMCryptoConstants()
    
    with zipfile.ZipFile(temp_apk.name, 'w') as apk_zip:
        # 创建一个简单的DEX文件
        dex_content = b'dex\n035\x00' + b'\x00' * 100
        apk_zip.writestr('classes.dex', dex_content)
        
        # 创建一个包含SM4 SBox的简单二进制文件
        # 直接将SM4 SBox作为原始字节数据
        sm4_sbox_data = bytes(constants.SM4_SBOX)
        
        # 创建一个简单的"SO"文件，实际上就是包含常量数据
        so_content = b'\x7fELF'  # ELF魔数
        so_content += b'\x00' * 100  # 填充
        so_content += sm4_sbox_data  # SM4 SBox数据
        so_content += b'\x00' * 100  # 更多填充
        
        # 添加SM2椭圆曲线参数（作为十六进制字符串）
        so_content += constants.SM2_CURVE_P.encode('ascii')
        so_content += b'\x00' * 50
        
        # 添加SM3常量
        for iv_val in constants.SM3_IV:
            iv_bytes = iv_val.to_bytes(4, 'big')
            so_content += iv_bytes
        
        apk_zip.writestr('lib/arm64-v8a/libtest.so', so_content)
        
        # 添加AndroidManifest.xml
        manifest = '''<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.test.sm">
    <application android:label="Test">
        <activity android:name=".SM2Activity" />
        <service android:name=".SM3Service" />
        <receiver android:name=".SM4Receiver" />
    </application>
</manifest>'''
        apk_zip.writestr('AndroidManifest.xml', manifest.encode('utf-8'))
    
    return temp_apk.name


def test_constant_detection():
    """测试常量检测功能"""
    print("测试常量检测功能...")
    
    constants = SMCryptoConstants()
    analyzer = APKAnalyzer("dummy.apk")  # 文件名不重要，我们只测试方法
    
    # 测试SM4 SBox检测
    sm4_sbox_hex = ''.join([hex(b)[2:].upper().zfill(2) for b in constants.SM4_SBOX])
    test_data = "前缀数据" + sm4_sbox_hex + "后缀数据"
    
    # 模拟在数据中查找常量
    all_constants = constants.get_all_constants()
    found_sm4 = False
    for algorithm, const_list in all_constants.items():
        for const_val in const_list:
            if const_val in test_data.upper():
                print(f"✓ 在测试数据中发现 {algorithm} 算法常量")
                if algorithm == 'SM4':
                    found_sm4 = True
    
    assert found_sm4, "未能检测到SM4常量"
    print("✓ 常量检测功能正常")


def test_raw_binary_detection():
    """测试原始二进制数据中的常量检测"""
    print("测试原始二进制数据检测...")
    
    constants = SMCryptoConstants()
    
    # 创建包含SM4 SBox的二进制数据
    test_binary = b'\x00' * 100  # 前缀
    test_binary += bytes(constants.SM4_SBOX)  # SM4 SBox
    test_binary += b'\x00' * 100  # 后缀
    
    # 转换为十六进制字符串进行检测
    hex_data = binascii.hexlify(test_binary).decode('utf-8').upper()
    
    # 检测SM4 SBox
    sm4_sbox_hex = ''.join([hex(b)[2:].upper().zfill(2) for b in constants.SM4_SBOX])
    
    if sm4_sbox_hex in hex_data:
        print("✓ 在二进制数据中成功检测到SM4 SBox")
    else:
        print("✗ 未能在二进制数据中检测到SM4 SBox")
        print(f"SBox长度: {len(sm4_sbox_hex)}")
        print(f"数据长度: {len(hex_data)}")
        print(f"SBox前10字符: {sm4_sbox_hex[:10]}")
        print(f"数据中是否包含: {'D690E9FE' in hex_data}")  # SM4 SBox的前几个字节


def test_apk_with_simple_detection():
    """使用简单的APK测试检测功能"""
    print("测试简单APK检测...")
    
    # 创建测试APK
    test_apk = create_simple_test_apk()
    
    try:
        # 手动检查APK内容
        with zipfile.ZipFile(test_apk, 'r') as apk_zip:
            so_data = apk_zip.read('lib/arm64-v8a/libtest.so')
            hex_data = binascii.hexlify(so_data).decode('utf-8').upper()
            
            constants = SMCryptoConstants()
            all_constants = constants.get_all_constants()
            
            found_algorithms = []
            for algorithm, const_list in all_constants.items():
                for const_val in const_list:
                    if const_val in hex_data:
                        found_algorithms.append(algorithm)
                        print(f"✓ 在SO文件中发现 {algorithm} 算法常量: {const_val[:20]}...")
            
            if found_algorithms:
                print(f"✓ 总共发现 {len(set(found_algorithms))} 种算法: {set(found_algorithms)}")
            else:
                print("✗ 未发现任何算法常量")
                
                # 调试信息
                print(f"SO文件大小: {len(so_data)} 字节")
                print(f"十六进制数据长度: {len(hex_data)} 字符")
                print(f"数据前100字符: {hex_data[:100]}")
                
                # 检查SM4 SBox的前几个字节
                sm4_start = 'D690E9FECC'  # SM4 SBox的前5个字节
                if sm4_start in hex_data:
                    print(f"✓ 发现SM4 SBox开始部分: {sm4_start}")
                else:
                    print(f"✗ 未发现SM4 SBox开始部分: {sm4_start}")
    
    finally:
        os.unlink(test_apk)


def main():
    """运行所有测试"""
    print("开始运行简单的国密算法检测测试...")
    print("="*60)
    
    try:
        test_constant_detection()
        test_raw_binary_detection()
        test_apk_with_simple_detection()
        
        print("="*60)
        print("✓ 所有简单测试通过！")
        
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
