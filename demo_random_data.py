#!/usr/bin/env python3
"""
演示DoIP随机数据生成功能
"""

import random
from colorama import init, Fore

def generate_completely_random_doip_data():
    """生成完全随机的DoIP数据（不基于任何模板）"""
    # 随机数据长度 (0-500字节)
    data_length = random.randint(0, 500)
    
    # 生成完全随机的数据
    random_data = bytes([random.randint(0, 255) for _ in range(data_length)])
    
    return random_data

def create_doip_message(payload_type, data=b"", source_addr=0x0E80, target_addr=0x1000):
    """创建DoIP消息"""
    # 根据不同的payload类型计算长度
    if payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息类型
        payload_length = 4 + len(data)  # 源地址(2) + 目标地址(2) + 数据长度
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
            (source_addr >> 8) & 0xFF,  # 源地址
            source_addr & 0xFF,
            (target_addr >> 8) & 0xFF,  # 目标地址
            target_addr & 0xFF,
        ])
        return doip_header + data
    else:
        # 其他类型的DoIP消息
        payload_length = len(data)
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
        ])
        return doip_header + data

def generate_fuzz_data_demo(base_data):
    """演示版本的模糊测试数据生成"""
    fuzzed_data = bytearray(base_data)
    
    # 随机选择变异策略
    strategies = ["byte_flip", "byte_random", "length_extend", "pattern_repeat", "null_injection"]
    selected_strategy = random.choice(strategies)
    
    if selected_strategy == "byte_flip":
        # 位翻转
        if len(fuzzed_data) > 0:
            pos = random.randint(0, len(fuzzed_data) - 1)
            bit_pos = random.randint(0, 7)
            fuzzed_data[pos] ^= (1 << bit_pos)
            
    elif selected_strategy == "byte_random":
        # 随机字节替换
        for _ in range(random.randint(1, 3)):
            if len(fuzzed_data) > 0:
                pos = random.randint(0, len(fuzzed_data) - 1)
                fuzzed_data[pos] = random.randint(0, 255)
                
    elif selected_strategy == "length_extend":
        # 扩展长度
        extend_count = random.randint(1, 50)
        extend_data = bytes([random.randint(0, 255) for _ in range(extend_count)])
        fuzzed_data.extend(extend_data)
        
    elif selected_strategy == "pattern_repeat":
        # 模式重复
        if len(fuzzed_data) > 0:
            pattern_length = min(random.randint(1, 4), len(fuzzed_data))
            pattern = fuzzed_data[:pattern_length]
            repeat_count = random.randint(2, 10)
            fuzzed_data.extend(pattern * repeat_count)
            
    elif selected_strategy == "null_injection":
        # 空字节注入
        null_count = random.randint(1, 5)
        if len(fuzzed_data) > 0:
            pos = random.randint(0, len(fuzzed_data))
            fuzzed_data[pos:pos] = b'\x00' * null_count
        else:
            fuzzed_data.extend(b'\x00' * null_count)
    
    return bytes(fuzzed_data), selected_strategy

def main():
    init()  # 初始化colorama
    
    print(Fore.CYAN + "=" * 80)
    print(Fore.CYAN + "              DoIP 随机数据生成演示")
    print(Fore.CYAN + "=" * 80)
    
    template_count = 0
    random_count = 0
    
    for i in range(15):
        print(Fore.MAGENTA + f"\n[  {i+1:02d}  ] 测试用例 {i+1}/15")
        
        # 随机选择数据生成方式
        use_template = random.choice([True, False])
        
        if use_template:
            # 基于模板的模糊测试
            base_data = bytes([0x22, 0xF1, 0x90])  # 示例：读取VIN
            fuzzed_data, strategy = generate_fuzz_data_demo(base_data)
            
            doip_message = create_doip_message(
                payload_type=0x8001,  # 诊断消息
                data=fuzzed_data,
                source_addr=random.randint(0x0E00, 0x0EFF),
                target_addr=random.randint(0x1000, 0x17FF)
            )
            
            print(Fore.BLUE + f"[  {i+1:02d}  ] 模板模糊测试")
            print(Fore.WHITE + f"        基础数据: {' '.join([f'{b:02X}' for b in base_data])}")
            print(Fore.WHITE + f"        变异策略: {strategy}")
            print(Fore.WHITE + f"        变异后长度: {len(fuzzed_data)} 字节")
            template_count += 1
            
        else:
            # 完全随机数据
            random_payload_type = random.randint(0x0000, 0xFFFF)
            random_data = generate_completely_random_doip_data()
            
            doip_message = create_doip_message(
                payload_type=random_payload_type,
                data=random_data,
                source_addr=random.randint(0x0000, 0xFFFF),
                target_addr=random.randint(0x0000, 0xFFFF)
            )
            
            print(Fore.CYAN + f"[  {i+1:02d}  ] 完全随机测试")
            print(Fore.WHITE + f"        载荷类型: 0x{random_payload_type:04X}")
            print(Fore.WHITE + f"        随机数据长度: {len(random_data)} 字节")
            random_count += 1
        
        # 显示DoIP消息信息
        print(Fore.GREEN + f"        DoIP消息长度: {len(doip_message)} 字节")
        
        # 显示消息预览
        if len(doip_message) > 16:
            preview = " ".join([f"{b:02X}" for b in doip_message[:16]])
            print(Fore.YELLOW + f"        消息预览: {preview}...")
        else:
            full_msg = " ".join([f"{b:02X}" for b in doip_message])
            print(Fore.YELLOW + f"        完整消息: {full_msg}")
    
    # 统计信息
    print(Fore.CYAN + "\n" + "=" * 80)
    print(Fore.CYAN + "                    演示统计")
    print(Fore.CYAN + "=" * 80)
    print(Fore.BLUE + f"基于模板的模糊测试: {template_count} 次 ({template_count/15*100:.1f}%)")
    print(Fore.CYAN + f"完全随机数据测试: {random_count} 次 ({random_count/15*100:.1f}%)")
    print(Fore.WHITE + f"总计测试用例: 15 次")
    print(Fore.CYAN + "=" * 80)
    print(Fore.GREEN + "演示完成！这展示了DoIP模糊测试工具的随机数据生成能力")

if __name__ == "__main__":
    main()
