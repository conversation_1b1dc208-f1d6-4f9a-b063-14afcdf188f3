#!/usr/bin/env python3
"""
随机数种子安全分析工具
该脚本分析提供文件中随机数种子的安全性。
它检查模式、熵值和其他不安全随机性的指标。
"""

import os
import sys
import math
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt
from scipy import stats

def load_seeds(filename):
    """从文件加载种子值"""
    try:
        with open(filename, 'r') as f:
            seeds = [line.strip() for line in f if line.strip()]
        return seeds
    except Exception as e:
        print(f"加载种子文件时出错: {e}")
        sys.exit(1)

def hex_to_int(hex_str):
    """将十六进制字符串转换为整数"""
    try:
        return int(hex_str, 16)
    except ValueError:
        print(f"无效的十六进制值: {hex_str}")
        return None

def calculate_entropy(data):
    """计算数据的香农熵"""
    counter = Counter(data)
    entropy = 0
    total = len(data)
    for count in counter.values():
        probability = count / total
        entropy -= probability * math.log2(probability)
    return entropy

def check_sequential_patterns(values):
    """检查值中的序列模式"""
    diffs = [values[i+1] - values[i] for i in range(len(values)-1)]
    diff_counter = Counter(diffs)
    most_common_diff, count = diff_counter.most_common(1)[0]
    
    pattern_percent = (count / len(diffs)) * 100
    return most_common_diff, pattern_percent

def analyze_byte_distribution(values_hex):
    """分析十六进制值中字节的分布"""
    all_bytes = []
    for hex_val in values_hex:
        # 将十六进制字符串分成字节（每2个字符）
        bytes_in_value = [hex_val[i:i+2] for i in range(0, len(hex_val), 2)]
        all_bytes.extend(bytes_in_value)
    
    # 将字节转换为整数进行分析
    byte_values = [int(b, 16) for b in all_bytes]
    
    # 分析分布
    byte_mean = np.mean(byte_values)
    byte_std = np.std(byte_values)
    byte_entropy = calculate_entropy(all_bytes)
    
    # 均匀分布的期望值
    expected_mean = 127.5  # 在[0, 255]范围内均匀分布的平均值
    expected_std = 73.9    # 在[0, 255]范围内均匀分布的标准差
    
    # Kolmogorov-Smirnov均匀性测试
    _, p_value = stats.kstest(byte_values, 'uniform', args=(0, 256))
    
    return {
        'mean': byte_mean,
        'std': byte_std,
        'expected_mean': expected_mean,
        'expected_std': expected_std,
        'entropy': byte_entropy,
        'max_entropy': 8.0,  # 字节值的最大熵（8位）
        'p_value': p_value
    }

def check_linear_correlation(values):
    """检查序列中的线性相关性"""
    if len(values) < 2:
        return 0
    
    x = list(range(len(values)))
    correlation, _ = stats.pearsonr(x, values)
    return correlation

def check_time_based_patterns(values):
    """
    检查基于时间的模式，例如使用系统时间作为种子。
    基于时间的种子通常显示特定的增量或模式。
    """
    diffs = [values[i+1] - values[i] for i in range(len(values)-1)]
    
    # 检查恒定或可预测的差异
    diff_std = np.std(diffs)
    diff_mean = np.mean(diffs)
    
    # 检查差异是否大部分恒定（低标准差）
    if diff_std / abs(diff_mean) < 0.1 and abs(diff_mean) > 0:
        return True, diff_mean
    
    # 检查典型的时间戳使用的小增量
    small_increments = [d for d in diffs if 0 < d < 1000000]
    if len(small_increments) > len(diffs) * 0.8:
        return True, np.mean(small_increments)
    
    return False, diff_mean

def run_diehard_tests(values, filename="temp_random_data.bin"):
    """
    建议运行Diehard测试（实际实现需要安装Diehard套件或其Python包装器）。
    """
    print("\n[!] 要进行更全面的分析，请考虑运行Diehard或NIST测试。")
    print("    这些测试需要专门的软件，超出了本脚本的范围。")

def analyze_repeating_patterns(values_hex):
    """查找种子值中的重复模式"""
    # 计算每个值的出现次数
    value_counts = Counter(values_hex)
    duplicates = {val: count for val, count in value_counts.items() if count > 1}
    
    # 寻找重复的子序列
    subsequence_found = False
    for length in range(3, min(10, len(values_hex) // 2)):
        for i in range(len(values_hex) - length * 2):
            subsequence = values_hex[i:i+length]
            for j in range(i + length, len(values_hex) - length + 1):
                if values_hex[j:j+length] == subsequence:
                    print(f"[!] 在位置 {i} 和 {j} 发现长度为 {length} 的重复子序列")
                    subsequence_found = True
                    break
            if subsequence_found:
                break
        if subsequence_found:
            break
    
    return len(duplicates), duplicates

def main():
    if len(sys.argv) != 2:
        print("用法: python seed_test.py <种子文件>")
        print("示例: python seed_test.py 7e3.txt")
        sys.exit(1)
    
    seed_file = sys.argv[1]
    print(f"[+] 分析 {seed_file} 中的随机种子值...")
    
    # 从文件加载种子
    seeds = load_seeds(seed_file)
    print(f"[+] 已加载 {len(seeds)} 个种子值")
    
    # 判断种子长度
    if seeds and len(seeds) > 0:
        seed_byte_length = len(seeds[0]) // 2  # 十六进制字符串，每2个字符表示1字节
        seed_bit_length = seed_byte_length * 8
        print(f"[+] 检测到种子长度为: {seed_byte_length} 字节 ({seed_bit_length} 位)")
        
        # 样本量建议
        print("\n=== 样本量建议 ===")
        print("根据信息论和统计学原理，不同长度的种子需要不同规模的样本才能得到可靠的熵估计：")
        
        # 标准长度的种子有特定的推荐值
        if seed_byte_length == 4:  # 4字节/32位种子
            min_samples = 1000
            good_samples = 10000
            optimal_samples = 50000
            full_samples = 200000
            print(f"【4字节(32位)种子 - 标准长度】:")
            
        elif seed_byte_length == 16:  # 16字节/128位种子
            min_samples = 2000
            good_samples = 20000
            optimal_samples = 100000
            full_samples = 500000
            print(f"【16字节(128位)种子 - 标准长度】:")
            
        elif seed_byte_length == 32:  # 32字节/256位种子
            min_samples = 3000
            good_samples = 30000
            optimal_samples = 150000
            full_samples = 1000000
            print(f"【32字节(256位)种子 - 标准长度】:")
            
        else:  # 非标准长度
            # 基于信息论计算科学的样本量建议
            # 按照香农采样定理和NIST推荐的方法估算
            
            # 最小可接受样本量：n*log2(n)，其中n是位长度
            min_factor = seed_bit_length * math.log2(seed_bit_length)
            min_samples = max(1000, int(min_factor * 2))
            
            # 推荐样本量：基于NIST SP800-90B的启发式方法，需要足够样本覆盖大部分可能状态
            good_samples = max(3000, int(min_samples * 5))
            
            # 优化样本量：一般需要原始样本量的5倍来检测隐藏模式
            optimal_samples = max(20000, int(good_samples * 5))
            
            # 全面安全验证：符合NIST严格要求，通常至少需要10^6比特数据
            full_samples = max(100000, int(1000000 / seed_bit_length))
            
            print(f"【{seed_byte_length}字节({seed_bit_length}位)种子 - 非标准长度】:")
            print("  • 注意：对于非标准长度种子，样本量是基于信息理论动态计算的")
            print(f"  • 计算依据：位长度({seed_bit_length})和NIST SP800-90B推荐方法")
        
        print(f"  • 最小可接受样本量: {min_samples:,} 个种子")
        print(f"    - 仅能进行初步评估")
        print(f"    - 可能无法检测微小模式或周期性")
        
        print(f"  • 推荐样本量: {good_samples:,} 个种子")
        print(f"    - 可提供较准确的熵估计和分布分析")
        print(f"    - 能检测大多数常见的随机性问题")
        
        print(f"  • 优化样本量: {optimal_samples:,} 个种子")
        print(f"    - 能发现隐藏的统计偏差和复杂模式")
        print(f"    - 适用于安全关键系统的评估")
        
        print(f"  • 全面安全验证: {full_samples:,} 个种子")
        print(f"    - 符合NIST SP800-90B等严格标准的要求")
        print(f"    - 适用于密码学核心组件验证")
        
        print("\n【理论依据】:")
        print("1. 香农熵估计理论: 可靠的熵估计需要样本量至少是可能状态数的对数倍")
        print("2. 最小样本原则: 对于n位随机数，至少需要约n·log₂(n)个样本才能观察到统计特性")
        print("3. 覆盖原则: 样本量应足够大以覆盖可能的位模式分布")
        print("4. NIST推荐: SP800-90B建议使用至少1,000,000位数据进行熵估计")
        
        # 当前样本评估
        current_sample_ratio = len(seeds) / good_samples
        print(f"\n【当前样本评估】:")
        print(f"  • 当前样本量: {len(seeds):,} 个种子")
        if len(seeds) < min_samples:
            print(f"  • 状态: 不足 (仅达到推荐量的{current_sample_ratio*100:.1f}%)")
            print(f"  • 建议: 样本量不足，结果可能不可靠。建议至少收集{min_samples:,}个种子。")
        elif len(seeds) < good_samples:
            print(f"  • 状态: 基础 (达到推荐量的{current_sample_ratio*100:.1f}%)")
            print(f"  • 建议: 可进行初步分析，但对精确安全评估样本量偏少。")
        elif len(seeds) < optimal_samples:
            print(f"  • 状态: 良好 (达到推荐量的{current_sample_ratio*100:.1f}%)")
            print(f"  • 建议: 样本量足够进行可靠分析，但对于关键安全系统仍可增加。")
        else:
            print(f"  • 状态: 优秀 (超过推荐量{current_sample_ratio:.1f}倍)")
            print(f"  • 建议: 样本量充足，可进行高精度安全评估。")
    
    # 将十六进制值转换为整数进行数值分析
    seed_ints = [hex_to_int(seed) for seed in seeds if hex_to_int(seed) is not None]
    
    # 基本统计
    print("\n=== 基本统计信息 ===")
    print(f"有效种子值数量: {len(seed_ints)}")
    print(f"最小值: {min(seed_ints):X}")
    print(f"最大值: {max(seed_ints):X}")
    
    print("\n" + "="*80)
    print("熵分析 (Entropy Analysis)")
    print("="*80)
    print("【作用】：熵是衡量随机性的关键指标。香农熵越高，表示数据的不确定性越大，随机性越好。")
    print("         真正随机的数据应具有接近理论最大值的熵。")
    print("【安全意义】：低熵值意味着数据中存在模式或重复，攻击者可能预测出未来的值。")
    print("             安全的随机数生成器应产生高熵值的输出。")
    print("【判断标准】：熵比率（实际熵/最大可能熵）应大于0.9，越接近1越好。")
    
    # 熵分析
    entropy = calculate_entropy(seeds)
    max_entropy = math.log2(len(seeds))
    print(f"\n=== 熵分析结果 ===")
    print(f"香农熵: {entropy:.4f} bits")
    print(f"最大可能熵: {max_entropy:.4f} bits")
    print(f"熵比率: {entropy/max_entropy:.4f}")
    print(f"解释: {'良好' if entropy/max_entropy > 0.9 else '存在问题'}")
    
    print("\n" + "="*80)
    print("序列模式分析 (Sequential Pattern Analysis)")
    print("="*80)
    print("【作用】：检测连续种子值之间是否存在固定的差异模式。")
    print("         安全的随机数不应显示连续值之间的关系。")
    print("【安全意义】：如果连续值之间的差异有规律，攻击者可能预测出后续值。")
    print("             例如，时间戳生成的数据通常会显示固定增量或可预测的差异。")
    print("【判断标准】：最常见差值的占比应低于20%，且不应存在明显的重复差值模式。")
    
    # 检查序列模式
    common_diff, pattern_percent = check_sequential_patterns(seed_ints)
    print(f"\n=== 序列模式分析结果 ===")
    print(f"连续值之间最常见的差值: {common_diff}")
    print(f"此差值的百分比: {pattern_percent:.2f}%")
    print(f"解释: {'存在问题' if pattern_percent > 20 else '可接受'}")
    
    print("\n" + "="*80)
    print("线性相关性分析 (Linear Correlation Analysis)")
    print("="*80)
    print("【作用】：检测种子值是否随生成顺序呈现线性增长或减少趋势。")
    print("         真正随机的数据不应与其生成顺序有相关性。")
    print("【安全意义】：线性相关表明数据生成可能基于简单的递增函数或计数器。")
    print("             线性模式易于预测，攻击者能估算出未来值。")
    print("【判断标准】：相关系数的绝对值应低于0.3，越接近0越好。")
    
    # 检查线性相关性
    correlation = check_linear_correlation(seed_ints)
    print(f"\n=== 线性相关性分析结果 ===")
    print(f"线性相关系数: {correlation:.4f}")
    print(f"解释: {'存在问题' if abs(correlation) > 0.3 else '可接受'}")
    
    print("\n" + "="*80)
    print("字节分布分析 (Byte Distribution Analysis)")
    print("="*80)
    print("【作用】：检查种子中各字节值(0-255)的分布是否均匀。")
    print("         理想随机数应有均匀分布的字节值，无明显偏好。")
    print("【安全意义】：非均匀分布表明生成算法有偏差，可能使用了弱随机源。")
    print("             均匀分布是强随机数的必要(但非充分)条件。")
    print("【判断标准】：KS检验p值应大于0.05，表明分布接近均匀。")
    print("             字节熵应接近8.0，表明所有可能的字节值出现概率相当。")
    
    # 字节分布分析
    print(f"\n=== 字节分布分析结果 ===")
    byte_stats = analyze_byte_distribution(seeds)
    print(f"字节值平均值: {byte_stats['mean']:.2f} (期望值: {byte_stats['expected_mean']})")
    print(f"标准差: {byte_stats['std']:.2f} (期望值: {byte_stats['expected_std']})")
    print(f"字节熵: {byte_stats['entropy']:.4f} bits (最大值: {byte_stats['max_entropy']})")
    print(f"均匀性测试 p值: {byte_stats['p_value']:.4f}")
    print(f"解释: {'良好' if byte_stats['p_value'] > 0.05 else '存在问题'}")
    
    print("\n" + "="*80)
    print("基于时间的模式分析 (Time-Based Pattern Analysis)")
    print("="*80)
    print("【作用】：检测种子值是否显示典型的基于时间的增长模式。")
    print("         安全的随机种子不应呈现时间戳特有的规律性增长。")
    print("【安全意义】：基于时间(如系统时钟)生成的随机数高度可预测。")
    print("             时间戳是常见但不安全的种子源，容易被攻击者猜测。")
    print("【判断标准】：应无明显的固定增量模式，差值应有高度随机性。")
    
    # 检查基于时间的模式
    is_time_based, diff_value = check_time_based_patterns(seed_ints)
    print(f"\n=== 基于时间的模式分析结果 ===")
    print(f"可能基于时间: {is_time_based}")
    if is_time_based:
        print(f"平均增量: {diff_value}")
        print("解释: 存在问题 - 种子可能基于时间戳")
    else:
        print("解释: 良好 - 未检测到明显的基于时间的模式")
    
    print("\n" + "="*80)
    print("重复模式分析 (Repeating Pattern Analysis)")
    print("="*80)
    print("【作用】：检测种子值中是否存在重复值或重复序列。")
    print("         真正的随机数序列中，重复的可能性应极低。")
    print("【安全意义】：重复的种子值直接导致重复的密钥或加密结果。")
    print("             这严重削弱加密强度，使系统容易受到重放攻击。")
    print("【判断标准】：在足够大的样本中，不应存在重复值或明显的重复模式。")
    
    # 检查重复模式
    print(f"\n=== 重复模式分析结果 ===")
    num_duplicates, duplicates = analyze_repeating_patterns(seeds)
    print(f"重复值数量: {num_duplicates}")
    if num_duplicates > 0:
        # 获取要显示的重复值数量（最多10个）
        display_count = min(10, len(duplicates))
        duplicates_to_show = list(duplicates.items())[:display_count]
        print(f"重复值（显示{display_count}个，共{num_duplicates}个）: {duplicates_to_show}")
        print("解释: 存在问题 - 安全的随机值不应重复")
    else:
        print("解释: 良好 - 未检测到重复值")
    
    # 总结
    print("\n=== 安全评估总结 ===")
    issues = []
    if entropy/max_entropy < 0.9:
        issues.append("低熵值")
    if pattern_percent > 20:
        issues.append("强序列模式")
    if abs(correlation) > 0.3:
        issues.append("线性相关性")
    if byte_stats['p_value'] < 0.05:
        issues.append("非均匀字节分布")
    if is_time_based:
        issues.append("基于时间的模式")
    if num_duplicates > 0:
        issues.append("重复值")
    
    if issues:
        print(f"[!] 检测到安全问题: {', '.join(issues)}")
        print("[!] 这些随机种子可能不适合安全用途。")
        
        print("\n=== 建议 ===")
        print("1、禁止使用C标准库函数random()、rand()生成随机数用于安全用途。")
        print("2、推荐使用真随机数产生器产生的随机数（一般调用芯片的硬件随机数产生接口），或者采用符合NIST SP800-90A/B、FIPS 1400-2、AIS 31、或者GMT005-2012标准实现的随机数产生器，用于密钥、IV、盐值的生成。")
        print("3、已知的可供产品使用的密码学安全的非物理真随机数产生器有：")
        print("   1）Linux操作系统的/dev/random设备接口")
        print("   2）Windows操作系统的CryptGenRandom接口")
        print("4、已知的可使用的密码学安全的伪随机数产生器包括：")
        print("   1）OpenSSL1.1.X的RAND_priv_bytes")
        print("   2）OpenSSL FIPS模块中实现的DRBG")
        print("   3）JDK的Java.security.SecureRandom")
        print("5、禁止使用Java的java.util.Random类生成随机数用于安全用途")
        print("6、使用密码学安全伪随机数产生器产生随机数时需要为伪随机数产生器设置安全的种子（如默认种子就是安全的，则无需手工设置），禁止每次都设置相同的种子或使用线性增长的数据作为种子（比如系统时间），这将导致每次产生的随机数都一样。")
    else:
        print("[+] 这些随机种子未检测到明显的安全问题。")
        print("[+] 但对于安全关键应用，仍建议进行更深入的密码学测试。")
    
    # 建议额外的测试
    run_diehard_tests(seed_ints)

if __name__ == "__main__":
    main()
