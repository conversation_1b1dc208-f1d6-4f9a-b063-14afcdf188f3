import os


# 证书转hex
def aaa():
    def strToHex(data):
        base = ''
        for each in data:
            base += hex(ord(each))[2:].rjust(2, '0')
        return base

    name = input('ECU Name: ')
    name = strToHex(name)
    while (len(name) < 20):
        name += '20'

    logicaladdr = input('Logical address: ')

    listfile = os.listdir()
    print(listfile)

    for each in listfile:
        if '.pem' in each:
            filename = each
    # filename = input('证书名：')
    #     # if '.pem' in filename:
    #     #     filename = each
    pdid = filename.split('_')[2].split('.')[0]
    print('pdid:', pdid)
    file = open(filename)
    data = file.read()
    data = data.rstrip()

    pdid = strToHex(pdid)
    pemdata = strToHex(data)
    length = hex(len(pemdata) // 2)[2:].rjust(4, '0')
    print("length:", length)
    data = '0100002d01' + name + logicaladdr + pdid + 'ff0100010100' + length + pemdata
    print(data)
    jixu = input("是否继续填充数据（y/n）：")
    if jixu == 'y':
        bbb(data)
    else:
        exit()


# 数据后方填充指定个数的00
def bbb(data=None):
    if data is None:
        data = input("your_data：")  # 替换为您的数据

    # 计算数据的字节长度
    data_length = len(data)
    print("当前数据长度为：", (data_length))
    # 计算需要填充的字节数
    count = input("请输入要填充至多少个‘0’的（字节数*2）：")
    padding_length = int(int(count) - int(data_length))
    # print("需要填充“0”的个数为:", padding_length)
    print("需要填充“0”的个数为:", padding_length, "；需要填充“0”的字节个数为", int(padding_length / 2))
    # 填充字节
    padded_data = data + "0" * padding_length
    # choice = input("初次灌装按1，更新灌装按2：")
    # if choice == '1':
    #     padded_data = '3101dd4600' + data + "0" * padding_length
    # else:
    #     padded_data = '3101dd4601' + data + "0" * padding_length

    # padded_data1 =padded_data.encode("utf-8")

    # 打印填充后的数据
    print("Padded data:", padded_data)
    # print(len(padded_data))
    # 打印填充后的数据的字节长度
    print("填充后字节长度为:", int(len(padded_data) / 2))


def del_space():
    space_data = input("Plz input data what you want to deal:")
    space_data = space_data.replace(" ", '')
    print("Dealed data:", space_data)

def AsciiToHex():
    data=input("Plz input data what you want to deal:")
    encoded_data = data.encode()

    # 将字节串转换为十六进制表示
    hex_data = encoded_data.hex()

    print(hex_data)


if __name__ == "__main__":
    moshi = input("选择功能（1:证书转hex；2：填充数据）： ")
    if moshi == '1':
        aaa()
    elif moshi == '2':
        bbb()
    elif moshi == '3':
        del_space()
    else:
        AsciiToHex()
