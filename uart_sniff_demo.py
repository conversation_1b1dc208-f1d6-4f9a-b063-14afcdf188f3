#!/usr/bin/env python3
"""
UART嗅探器演示脚本
演示如何使用UARTSniffer类进行数据嗅探和空闲检测
"""

import time
import sys
from uart import UARTSniffer

def interactive_demo():
    """交互式演示"""
    print("=== UART嗅探器交互式演示 ===")
    
    # 获取用户配置
    port = input(f"请输入串口号 (默认: COM3): ").strip() or "COM3"
    
    try:
        baudrate = int(input(f"请输入波特率 (默认: 115200): ").strip() or "115200")
    except ValueError:
        baudrate = 115200
    
    try:
        idle_timeout = float(input(f"请输入空闲超时时间/秒 (默认: 0.5): ").strip() or "0.5")
    except ValueError:
        idle_timeout = 0.5
    
    send_data_str = input(f"请输入要发送的数据(十六进制，默认: 01020304): ").strip() or "01020304"
    try:
        send_data = bytes.fromhex(send_data_str)
    except ValueError:
        print("无效的十六进制数据，使用默认值")
        send_data = b'\x01\x02\x03\x04'
    
    print(f"\n配置信息:")
    print(f"串口: {port}")
    print(f"波特率: {baudrate}")
    print(f"空闲超时: {idle_timeout}秒")
    print(f"发送数据: {send_data.hex()}")
    
    # 创建嗅探器
    sniffer = UARTSniffer(
        port=port,
        baudrate=baudrate,
        idle_timeout=idle_timeout,
        send_data=send_data
    )
    
    try:
        print("\n正在启动嗅探器...")
        if sniffer.start_sniffing():
            print("嗅探器已启动！")
            print("\n可用命令:")
            print("  's' - 手动发送数据")
            print("  'q' - 退出程序")
            print("  其他 - 显示帮助")
            print("\n开始监控UART通道...\n")
            
            while True:
                try:
                    cmd = input().strip().lower()
                    
                    if cmd == 'q':
                        break
                    elif cmd == 's':
                        sniffer.send_manual_data()
                        print("已手动发送数据")
                    else:
                        print("可用命令: 's'(发送), 'q'(退出)")
                        
                except EOFError:
                    break
                    
        else:
            print("无法启动嗅探器，请检查串口配置")
            
    except KeyboardInterrupt:
        print("\n收到中断信号...")
    finally:
        print("正在停止嗅探器...")
        sniffer.stop_sniffing()
        print("演示结束")


def auto_demo():
    """自动演示模式"""
    print("=== UART嗅探器自动演示 ===")
    
    # 使用默认配置
    sniffer = UARTSniffer(
        port="COM3",
        baudrate=115200,
        idle_timeout=1.0,  # 1秒空闲超时
        send_data=b'\xAA\xBB\xCC\xDD'
    )
    
    try:
        print("启动嗅探器...")
        if sniffer.start_sniffing():
            print("嗅探器已启动，将运行30秒自动演示")
            
            # 运行30秒
            for i in range(30):
                time.sleep(1)
                
                # 每5秒手动发送一次数据
                if i % 5 == 0 and i > 0:
                    print(f"\n[演示] 第{i}秒 - 手动发送数据")
                    sniffer.send_manual_data(b'\xFF\xEE\xDD\xCC')
                
                print(f"运行中... {i+1}/30秒", end='\r')
            
            print("\n自动演示完成")
            
        else:
            print("无法启动嗅探器")
            
    except KeyboardInterrupt:
        print("\n演示被中断")
    finally:
        sniffer.stop_sniffing()


def simple_sniff():
    """简单嗅探模式"""
    print("=== 简单UART嗅探模式 ===")
    print("使用默认配置进行嗅探，按Ctrl+C停止")
    
    sniffer = UARTSniffer()
    
    try:
        if sniffer.start_sniffing():
            print("嗅探器已启动，正在监控...")
            while True:
                time.sleep(1)
        else:
            print("无法启动嗅探器")
    except KeyboardInterrupt:
        print("\n停止嗅探")
    finally:
        sniffer.stop_sniffing()


def main():
    """主函数"""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "interactive" or mode == "i":
            interactive_demo()
        elif mode == "auto" or mode == "a":
            auto_demo()
        elif mode == "simple" or mode == "s":
            simple_sniff()
        else:
            print("未知模式，使用默认交互模式")
            interactive_demo()
    else:
        print("UART嗅探器演示脚本")
        print("\n使用方法:")
        print("  python uart_sniff_demo.py interactive  # 交互式演示")
        print("  python uart_sniff_demo.py auto         # 自动演示")
        print("  python uart_sniff_demo.py simple       # 简单嗅探")
        print("\n选择模式 (默认: interactive):")
        print("  1. interactive - 交互式配置和控制")
        print("  2. auto - 自动演示30秒")
        print("  3. simple - 简单嗅探模式")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "2":
            auto_demo()
        elif choice == "3":
            simple_sniff()
        else:
            interactive_demo()


if __name__ == "__main__":
    main()
