"""
DAB (Data Analysis Battery) tests for random number generators.
These tests focus on advanced statistical analysis of random data.
"""

import math
import numpy as np
from typing import Dict, List, Optional
from collections import Counter

from generators.base_generator import BaseGenerator
from utils.statistics import StatisticsUtils


class DABTests:
    """Implementation of DAB tests"""
    
    def __init__(self, generator: BaseGenerator):
        self.generator = generator
        self.stats = StatisticsUtils()
    
    def dab_bytedistrib(self, tsamples: int = 51200000, psamples: int = 1) -> Dict:
        """
        Byte distribution test - tests the distribution of bytes.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate bytes
            byte_counts = Counter()
            for _ in range(tsamples):
                byte_val = self.generator.random_uint32() & 0xFF
                byte_counts[byte_val] += 1
            
            # Expected frequency for each byte value
            expected_freq = tsamples / 256
            
            # Chi-square test
            observed = [byte_counts.get(i, 0) for i in range(256)]
            expected = [expected_freq] * 256
            
            _, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'dab_bytedistrib',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def dab_dct(self, tsamples: int = 50000, psamples: int = 1, block_size: int = 256) -> Dict:
        """
        Discrete Cosine Transform test - tests frequency domain properties.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate data blocks and apply DCT
            dct_coefficients = []
            
            for _ in range(tsamples // block_size):
                # Generate block of random data
                block = [self.generator.random_float() for _ in range(block_size)]
                
                # Apply simplified DCT (using numpy's FFT as approximation)
                block_array = np.array(block)
                dct_block = np.fft.fft(block_array).real
                dct_coefficients.extend(dct_block)
            
            # Test if DCT coefficients follow expected distribution
            if dct_coefficients:
                # Normalize coefficients
                mean_coeff = np.mean(dct_coefficients)
                std_coeff = np.std(dct_coefficients)
                
                if std_coeff > 0:
                    normalized = [(c - mean_coeff) / std_coeff for c in dct_coefficients]
                    _, p_val = self.stats.kolmogorov_smirnov_test(normalized, 'normal')
                else:
                    p_val = 0.5
            else:
                p_val = 0.5
            
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'dab_dct',
            'ntup': block_size,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def dab_filltree(self, tsamples: int = 15000000, psamples: int = 1, ntup: int = 32) -> Dict:
        """
        Fill tree test - tests tree-filling properties of random data.
        """
        p_values = []
        
        for _ in range(psamples):
            # Simulate filling a binary tree with random data
            tree_levels = int(math.log2(ntup)) + 1
            tree_nodes = [0] * (2**tree_levels - 1)
            
            for _ in range(tsamples):
                # Generate random path through tree
                path = self.generator.random_uint32() % ntup
                
                # Follow path and increment nodes
                node_index = 0
                for level in range(tree_levels - 1):
                    tree_nodes[node_index] += 1
                    if path & (1 << (tree_levels - 2 - level)):
                        node_index = 2 * node_index + 2  # Right child
                    else:
                        node_index = 2 * node_index + 1  # Left child
            
            # Test distribution of node counts
            if tree_nodes:
                # Expected counts based on tree structure
                expected_counts = []
                for level in range(tree_levels - 1):
                    level_nodes = 2**level
                    expected_count = tsamples / level_nodes
                    expected_counts.extend([expected_count] * level_nodes)
                
                # Chi-square test
                observed = tree_nodes[:len(expected_counts)]
                _, p_val = self.stats.chi_square_test(observed, expected_counts)
            else:
                p_val = 0.5
            
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'dab_filltree',
            'ntup': ntup,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def dab_filltree2(self, tsamples: int = 5000000, psamples: int = 1, ntup: int = 0) -> Dict:
        """
        Fill tree test variant 2 - alternative tree-filling test.
        """
        p_values = []
        
        for _ in range(psamples):
            # Different tree filling strategy
            tree_depth = 10  # Fixed depth
            leaf_counts = Counter()
            
            for _ in range(tsamples):
                # Generate random walk to leaf
                path = 0
                for depth in range(tree_depth):
                    bit = self.generator.random_uint32() & 1
                    path = (path << 1) | bit
                
                leaf_counts[path] += 1
            
            # Test uniformity of leaf distribution
            num_leaves = 2**tree_depth
            expected_freq = tsamples / num_leaves
            
            observed = [leaf_counts.get(i, 0) for i in range(num_leaves)]
            expected = [expected_freq] * num_leaves
            
            _, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'dab_filltree2',
            'ntup': ntup,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def dab_monobit2(self, tsamples: int = 65000000, psamples: int = 1, block_size: int = 12) -> Dict:
        """
        Monobit test variant 2 - tests bit distribution in blocks.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate blocks and count bits
            block_counts = []
            
            for _ in range(tsamples // block_size):
                # Generate block of bits
                bits = self.generator.random_bits(block_size)
                ones_count = sum(bits)
                block_counts.append(ones_count)
            
            # Test distribution of ones counts
            # Should follow binomial distribution
            expected_probs = [self.stats.binomial_probability(block_size, k, 0.5) 
                            for k in range(block_size + 1)]
            expected = [prob * len(block_counts) for prob in expected_probs]
            
            # Observed frequencies
            observed = [0] * (block_size + 1)
            for count in block_counts:
                observed[count] += 1
            
            _, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'dab_monobit2',
            'ntup': block_size,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def _combine_p_values(self, p_values: List[float]) -> float:
        """Combine multiple p-values using Fisher's method"""
        if not p_values:
            return 0.5
        
        # Fisher's combined probability test
        chi_sq = -2 * sum(math.log(max(p, 1e-10)) for p in p_values)
        df = 2 * len(p_values)
        
        # Convert to p-value
        from scipy.stats import chi2
        combined_p = 1 - chi2.cdf(chi_sq, df)
        
        return max(min(combined_p, 1.0), 0.0)
