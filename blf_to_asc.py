import can

#选择通道:转换含有指定网络ID信号的通道数据
def find_channel(input_file):
    with open(input_file, 'rb') as blf:
        log = can.BLFReader(blf)
        for msg in log:
            if msg.arbitration_id == 0x41E:
                #print(msg.timestamp,signal_values[])
                print(msg)
                channel_id = msg.channel
                print(channel_id)
                break
    return channel_id


def blf_to_asc(input_file, output_file,channel_id):
    with open(input_file, 'rb') as blf:
        with open(output_file, 'w') as asc:import can

#选择通道:转换含有指定网络ID信号的通道数据
def find_channel(input_file):
    with open(input_file, 'rb') as blf:
        log = can.BLFReader(blf)
        for msg in log:
            if msg.arbitration_id == 0x41E:
                #print(msg.timestamp,signal_values[])
                print(msg)
                channel_id = msg.channel
                print(channel_id)
                break
    return channel_id


def blf_to_asc(input_file, output_file,channel_id):
    with open(input_file, 'rb') as blf:
        with open(output_file, 'w') as asc:
            log = can.BLFReader(blf)
            log_out = can.io.ASCWriter(asc)
            for msg in log:
                #asc.write(f"{msg.timestamp} {msg.channel} {msg.arbitration_id:x} {msg.dlc} {' '.join(map(hex, msg.data))}\n")
                if channel_id == msg.channel:
                    log_out.on_message_received(msg)
                    print(msg)
            log_out.stop()


if __name__ == "__main__":
    input_file = ""
    output_file = "target.asc"
    channel_id = find_channel(input_file)
    blf_to_asc(input_file, output_file,channel_id)
            log = can.BLFReader(blf)
            log_out = can.io.ASCWriter(asc)
            for msg in log:
                #asc.write(f"{msg.timestamp} {msg.channel} {msg.arbitration_id:x} {msg.dlc} {' '.join(map(hex, msg.data))}\n")
                if channel_id == msg.channel:
                    log_out.on_message_received(msg)
                    print(msg)
            log_out.stop()


if __name__ == "__main__":
    input_file = "./source.blf"
    output_file = "./target.asc"
    channel_id = find_channel(input_file)
    blf_to_asc(input_file, output_file,channel_id)