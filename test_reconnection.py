#!/usr/bin/env python3
"""
测试TCP重连功能的演示脚本
"""

import socket
import time
import random
from colorama import init, Fore

def establish_tcp_connection(target_ip, target_port, retry_count=3, retry_delay=2):
    """建立TCP连接，支持重试"""
    for attempt in range(retry_count):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(5)  # 设置超时时间
            s.connect((target_ip, target_port))
            print(Fore.GREEN + f"[  TCP  ] 成功连接到 {target_ip}:{target_port}")
            return s
        except socket.error as msg:
            if attempt < retry_count - 1:
                print(Fore.YELLOW + f"[  TCP  ] 连接失败 (尝试 {attempt + 1}/{retry_count}): {msg}")
                print(Fore.YELLOW + f"[  TCP  ] {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print(Fore.RED + f"[  TCP  ] 连接失败 (所有尝试已用完): {msg}")
    return None

def check_connection_alive(sock):
    """检查TCP连接是否仍然活跃"""
    try:
        # 发送一个空的数据包来检查连接状态
        sock.settimeout(1)
        sock.send(b'')
        return True
    except socket.error:
        return False
    except Exception:
        return False

def simulate_connection_test(target_ip="127.0.0.1", target_port=8080):
    """模拟连接测试"""
    init()  # 初始化colorama
    
    print(Fore.CYAN + "=" * 60)
    print(Fore.CYAN + "         TCP 重连功能测试")
    print(Fore.CYAN + "=" * 60)
    print(Fore.WHITE + f"目标地址: {target_ip}:{target_port}")
    print(Fore.YELLOW + "注意: 这是一个演示脚本，会尝试连接到指定地址")
    print(Fore.YELLOW + "如果目标不存在，将演示重连逻辑")
    print(Fore.CYAN + "=" * 60)
    
    # 尝试建立初始连接
    sock = establish_tcp_connection(target_ip, target_port, retry_count=3, retry_delay=1)
    
    if sock:
        print(Fore.GREEN + "[  成功  ] 初始连接建立成功")
        
        # 模拟一些操作
        for i in range(5):
            print(Fore.BLUE + f"[  {i+1:02d}  ] 检查连接状态...")
            
            if check_connection_alive(sock):
                print(Fore.GREEN + f"[  {i+1:02d}  ] 连接正常")
                
                # 尝试发送一些数据
                try:
                    test_data = f"Test message {i+1}".encode()
                    sock.send(test_data)
                    print(Fore.GREEN + f"[  {i+1:02d}  ] 数据发送成功")
                except socket.error as e:
                    print(Fore.RED + f"[  {i+1:02d}  ] 数据发送失败: {e}")
                    break
            else:
                print(Fore.YELLOW + f"[  {i+1:02d}  ] 连接已断开，尝试重连...")
                sock.close()
                sock = establish_tcp_connection(target_ip, target_port, retry_count=2, retry_delay=1)
                if not sock:
                    print(Fore.RED + f"[  {i+1:02d}  ] 重连失败")
                    break
                else:
                    print(Fore.GREEN + f"[  {i+1:02d}  ] 重连成功")
            
            time.sleep(2)
        
        # 关闭连接
        if sock:
            sock.close()
            print(Fore.GREEN + "[  完成  ] 连接已关闭")
            
    else:
        print(Fore.RED + "[  失败  ] 无法建立初始连接")
        print(Fore.YELLOW + "[  演示  ] 以下展示重连逻辑:")
        
        # 演示重连逻辑
        for attempt in range(3):
            print(Fore.YELLOW + f"[  重连  ] 尝试重连 {attempt + 1}/3...")
            sock = establish_tcp_connection(target_ip, target_port, retry_count=1, retry_delay=0)
            if sock:
                print(Fore.GREEN + f"[  重连  ] 第 {attempt + 1} 次尝试成功")
                sock.close()
                break
            else:
                print(Fore.RED + f"[  重连  ] 第 {attempt + 1} 次尝试失败")
                time.sleep(1)
    
    print(Fore.CYAN + "\n" + "=" * 60)
    print(Fore.CYAN + "         测试完成")
    print(Fore.CYAN + "=" * 60)
    print(Fore.WHITE + "重连功能演示结束")

def main():
    print(Fore.CYAN + "TCP重连功能测试脚本")
    print(Fore.WHITE + "1. 测试本地连接 (127.0.0.1:8080)")
    print(Fore.WHITE + "2. 测试自定义地址")
    print(Fore.WHITE + "3. 退出")
    
    choice = input(Fore.YELLOW + "请选择 (1-3): ").strip()
    
    if choice == "1":
        simulate_connection_test("127.0.0.1", 8080)
    elif choice == "2":
        target_ip = input(Fore.YELLOW + "请输入目标IP: ").strip()
        target_port = int(input(Fore.YELLOW + "请输入目标端口: ").strip())
        simulate_connection_test(target_ip, target_port)
    elif choice == "3":
        print(Fore.GREEN + "退出程序")
    else:
        print(Fore.RED + "无效选择")

if __name__ == "__main__":
    main()
