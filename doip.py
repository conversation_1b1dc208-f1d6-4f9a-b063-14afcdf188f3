import cmd2
import getopt
import sys
import json
import os
import binascii
from utils.doip_socket import *
from scapy.sendrecv import sr1
from scapy.layers.inet import *
from scapy.contrib.automotive.uds_scan import *
from cryptography.hazmat.primitives import cmac
from cryptography.hazmat.primitives.ciphers import algorithms
import logging
logging.basicConfig(filename='doip.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from scapy.main import load_contrib
load_contrib("automotive.doip")
load_contrib('automotive.uds')

negativeResponseCodes = {
    0x00: "POSITIVE_RESPONSE",
    0x10: "GENERAL_REJECT",
    0x11: "SERVICE_NOT_SUPPORTED",
    0x12: "SUB_FUNCTION_NOT_SUPPORTED",
    0x13: "INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT",
    0x14: "RESPONSE_TOO_LONG",
    0x21: "BUSY_REPEAT_REQUEST",
    0x22: "CONDITIONS_NOT_CORRECT",
    0x24: "REQUEST_SEQUENCE_ERROR",
    0x25: "NO_RESPONSE_FROM_SUBNET_COMPONENT",
    0x26: "FAILURE_PREVENTS_EXECUTION_OF_REQUESTED_ACTION",
    0x31: "REQUEST_OUT_OF_RANGE",
    0x33: "SECURITY_ACCESS_DENIED",
    0x35: "INVALID_KEY",
    0x36: "EXCEEDED_NUMBER_OF_ATTEMPTS",
    0x37: "REQUIRED_TIME_DELAY_NOT_EXPIRED",
    0x70: "UPLOAD_DOWNLOAD_NOT_ACCEPTED",
    0x71: "TRANSFER_DATA_SUSPENDED",
    0x72: "GENERAL_PROGRAMMING_FAILURE",
    0x73: "WRONG_BLOCK_SEQUENCE_COUNTER",
    0x78: "REQUEST_CORRECTLY_RECEIVED_RESPONSE_PENDING",
    0x7E: "SUB_FUNCTION_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x7F: "SERVICE_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x81: "RPM_TOO_HIGH",
    0x82: "RPM_TOO_LOW",
    0x83: "ENGINE_IS_RUNNING",
    0x84: "ENGINE_IS_NOT_RUNNING",
    0x85: "ENGINE_RUN_TIME_TOO_LOW",
    0x86: "TEMPERATURE_TOO_HIGH",
    0x87: "TEMPERATURE_TOO_LOW",
    0x88: "VEHICLE_SPEED_TOO_HIGH",
    0x89: "VEHICLE_SPEED_TOO_LOW",
    0x8A: "THROTTLE_PEDAL_TOO_HIGH",
    0x8B: "THROTTLE_PEDAL_TOO_LOW",
    0x8C: "TRANSMISSION_RANGE_NOT_IN_NEUTRAL",
    0x8D: "TRANSMISSION_RANGE_NOT_IN_GEAR",
    0x8F: "BRAKE_SWITCHES_NOT_CLOSED",
    0x90: "SHIFT_LEVER_NOT_IN_PARK",
    0x91: "TORQUE_CONVERTER_CLUTCH_LOCKED",
    0x92: "VOLTAGE_TOO_HIGH",
    0x93: "VOLTAGE_TOO_LOW"
}

UDS_SERVICE_NAMES = {
    0x10: "DIAGNOSTIC_SESSION_CONTROL",
    0x11: "ECU_RESET",
    0x14: "CLEAR_DIAGNOSTIC_INFORMATION",
    0x19: "READ_DTC_INFORMATION",
    0x20: "RETURN_TO_NORMAL",
    0x22: "READ_DATA_BY_IDENTIFIER",
    0x23: "READ_MEMORY_BY_ADDRESS",
    0x24: "READ_SCALING_DATA_BY_IDENTIFIER",
    0x27: "SECURITY_ACCESS",
    0x28: "COMMUNICATION_CONTROL",
    0x2A: "READ_DATA_BY_PERIODIC_IDENTIFIER",
    0x2C: "DYNAMICALLY_DEFINE_DATA_IDENTIFIER",
    0x2D: "DEFINE_PID_BY_MEMORY_ADDRESS",
    0x2E: "WRITE_DATA_BY_IDENTIFIER",
    0x2F: "INPUT_OUTPUT_CONTROL_BY_IDENTIFIER",
    0x31: "ROUTINE_CONTROL",
    0x34: "REQUEST_DOWNLOAD",
    0x35: "REQUEST_UPLOAD",
    0x36: "TRANSFER_DATA",
    0x37: "REQUEST_TRANSFER_EXIT",
    0x38: "REQUEST_FILE_TRANSFER",
    0x3D: "WRITE_MEMORY_BY_ADDRESS",
    0x3E: "TESTER_PRESENT",
    0x7F: "NEGATIVE_RESPONSE",
    0x83: "ACCESS_TIMING_PARAMETER",
    0x84: "SECURED_DATA_TRANSMISSION",
    0x85: "CONTROL_DTC_SETTING",
    0x86: "RESPONSE_ON_EVENT",
    0x87: "LINK_CONTROL"

}

payload_types = {
    0x0000: "Generic DoIP header NACK",
    0x0001: "Vehicle identification request",
    0x0002: "Vehicle identification request with EID",
    0x0003: "Vehicle identification request with VIN",
    0x0004: "Vehicle announcement message/vehicle identification response message",  # noqa: E501
    0x0005: "Routing activation request",
    0x0006: "Routing activation response",
    0x0007: "Alive check request",
    0x0008: "Alive check response",
    0x4001: "DoIP entity status request",
    0x4002: "DoIP entity status response",
    0x4003: "Diagnostic power mode information request",
    0x4004: "Diagnostic power mode information response",
    0x8001: "Diagnostic message",
    0x8002: "Diagnostic message ACK",
    0x8003: "Diagnostic message NACK"}

def get_doip(socket, doip, each, verbos = True):
    socket.ins.settimeout(0.05)
    resp = None
    pkt =  doip / UDS(binascii.a2b_hex(each))
    socket.send(pkt)
    save_log(pkt,1)
    try:
        resp = socket.recv_raw(65535)
        resp = DoIP(resp[1])
        if resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
            resp = socket.recv_raw(65535)
            resp = DoIP(resp[1])

        if resp[UDS].service == 0x7f:
            if each[:2] == '22' and verbos:
                print(each[2:],'\t',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
            elif each[:2] == '2e' and verbos:
                print(each[2:6],'\t',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
            elif verbos:
                print(each,' ',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
        else:
            if each[:2] == '10':
                dealSessionData(resp)
            elif each[:2] == '27':
                deal_securityAccess(resp)
            elif int(each[:2],16) == 0x2e:
                print(each[2:6],'\t','Success')
    except:
        pass
    finally:
        if resp:
            save_log(resp,0)
        return resp


def udpData(ip):
    packet = IP(dst=ip)/UDP(dport=13400)/DoIP(payload_type=1)
    resp = sr1(packet,verbose=False, timeout=1)
    return resp[DoIP].logical_address

def check_target(target):
    if target is None:
        return 0
    else:
        return 1

def check_socket(socket):
    if socket is None:
        return 0
    else:
        return 1

def judgeClient(ip):
    pkt = sr1(IP(dst=ip)/TCP(dport=13400, flags="S"), timeout=1, verbose=0)
    if pkt and pkt.haslayer(TCP) and pkt[TCP].flags == "SA":
        return 1
    else:
        return 0

def routineCTL(data, filename):
    pass


def pt2e(doip, socket, filename='dids.json'):
    
    with open(filename,'r') as f:
        content = json.load(f)
    for k,v in content.items():
        data = '2e' + k + '1' * len(v)
        get_doip(socket, doip, data, verbos = True)
        
def deal_scan_data(socket, doip, scan_range, num):
    socket.ins.settimeout(0.03)
    resp = None
    for each in scan_range:
        pkt =  doip / UDS(binascii.a2b_hex(each))
        socket.send(pkt)
        save_log(pkt,1)
        try:
            resp = socket.recv_raw(65535)
            resp = DoIP(resp[1])
            if resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
                resp = socket.recv_raw(65535)
                resp = DoIP(resp[1])

            if (num & 0b100 == 0b100) and resp[UDS_NR].negativeResponseCode != 0x11:
                print('0x'+each[:2],' ', UDS_SERVICE_NAMES[int(each[:2],16)])

            if (resp[UDS].service != 0x7f) or (num & 0b010 == 0b010 or num & 0b001 == 0b001 and resp[UDS_NR].negativeResponseCode != 0x12):
                print(' '.join(each[i:i+2] for i in range(0, len(each), 2)))
        except:
            pass
        finally:
            if resp:
                save_log(resp,0)





def scan_func(socket,source,target,num):
    doip = DoIP(payload_type=0x8001, source_address=source, target_address=target)
    if num & 0b100 == 0b100:
        logger.info('Scanning for Support Services......')
        print('\nSupport Services: ')
        scan_range = (x for x in range(0x100) if not x & 0x40)
        senddata = (hex(each)[2:].rjust(2,'0') + '00' for each in scan_range)
        deal_scan_data(socket, doip, senddata, 0b100)
        logger.info('--------------END--------------')

    if num & 0b010 == 0b010:
        logger.info('Scanning for Support Sessions......')
        print('\nSupport Sessions: ')
        session_range = range(0xff, 0, -1)
        senddata = ('10' + hex(each)[2:].rjust(2,'0') for each in session_range)
        deal_scan_data(socket, doip, senddata, 0b010)
        logger.info('--------------END--------------')

    if num & 0b001 == 0b001:
        time.sleep(1)
        logger.info('Scanning for Support Security Level......')
        print('\n')
        if get_doip(socket, doip, '1003'):
            print('Security Levels: ')
            scan_range = range(1, 256, 2)
            senddata = ('27' + hex(each)[2:].rjust(2,'0') for each in scan_range)
            deal_scan_data(socket, doip, senddata, 0b001)
            logger.info('--------------END--------------')
        else:
            print('Change Session Failed！！！')

def save_log(data,send):
    data = binascii.b2a_hex(raw(data)).decode('utf-8').upper()
    data = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
    if send:
        logger.info('Sender   --> '+data)
    else:
        logger.info('Receiver <-- '+data)


def my_cmac(key,data):
    c = cmac.CMAC(algorithms.AES(binascii.a2b_hex(key)))
    c.update(binascii.a2b_hex(data))
    return (binascii.b2a_hex(c.finalize()).decode('utf-8'))

def dealSessionData(resp):
    if resp.lastlayer().diagnosticSessionType == 2:
        print('Change Programing Session')
    elif resp.lastlayer().diagnosticSessionType == 3:
        print('Change Extend Session')
    else:
        print('Changed Session')

def deal_securityAccess(resp):
    if resp[UDS].securityAccessType % 2 == 0:
        print('Bypass 27...')

def getseed(number, data, doip,socket):
    seed_list = []
    for i in range(number):
        resp = get_doip(socket, doip, data)
        if resp and resp[UDS].service != 0x7f:
            seed = binascii.b2a_hex(resp[UDS_SAPR].securitySeed).decode('utf-8')
            print('Seed:',seed)
            seed_list.append(seed)
    return seed_list 


class MyApp(cmd2.Cmd):
    def __init__(self):
        cmd2.Cmd.__init__(self)
        del cmd2.Cmd.do_edit
        del cmd2.Cmd.do_history
        del cmd2.Cmd.do_macro
        del cmd2.Cmd.do_run_pyscript
        del cmd2.Cmd.do_run_script
        del cmd2.Cmd.do_set
        del cmd2.Cmd.do_shell
        del cmd2.Cmd.do_shortcuts
        del cmd2.Cmd.do_alias
        self.socket = None
        self.target = None
        self.key = None
        self.source = 0x0e80
        self.seedlist = None


        self.prompt = 'DoIP> '
        self.aliases.update({
            'q': 'quit',
            'h': 'help'
        })

    
    def do_connect(self, args):
        """
        Connect the ECU
        Usage:   connect server_ip
        example: connect ***********
        """
        ip = str(args)
        client = judgeClient(ip)

        if client:
            self.target = udpData(ip)
            logger.info('Connect '+ip+':13400')
        else:
            logger.info('bind 13400 listen......')
        self.socket = my_DoIPSocket(ip=ip,clientType=client)

        # args = args.split(' ')
        # client = True

        # self.socket = my_DoIPSocket(ip=args[0],port=int(args[1],10),clientType=client)
        if hasattr(self.socket,'target_address'):
            self.target = self.socket.target_address

    
    def do_scan(self,args):
        """
        Scan support Services、Sessions and Security Levels
        Usage:   
        scan   #means scan Services、Sessions and Security Levels
        scan name

        example: 
        scan session
        scan security
        scan
        scan service session
        """
        if self.socket is None:
            print('Please connect first')
        elif self.target is None:
            print('Please Set target address manually')
        else:
            num = 0

            if args == '':
                num |= 0b111

            else:
                if 'session' in args:
                    num |= 0b010
                if 'security' in args:
                    num |= 0b001
                if 'service' in args:
                    num |= 0b100

            scan_func(self.socket,self.source,self.target,num)
        
    def do_bruteTarget(self,args):
        """
        Brute Force the Target Address
        begin default 0x0,end default 0xffff
        Usage:
        bruteTarget
        bruteTarget begin
        bruteTarget begin end

        example: 
        bruteTarget
        bruteTarget 0x1234
        bruteTarget 1234 5678
        """
        if self.socket is None:
            print('Please connect first')
        else:
            if args == '':
                begin = 0x0
                end = 0x10000
            else:
                ss = args.split(' ')

                if len(ss) < 2:
                    begin = int(ss[0],16)
                    end = 0x10000
                else:
                    begin = int(ss[0],16)
                    end = int(ss[1],16)
            flag = 0

            logger.info('Brute Force the Target Address......')
            logger.info('From '+hex(begin)+' to '+hex(end))

            for i in range(begin, end+1):
                pkt = DoIP(payload_type=0x8001, source_address=self.source, target_address=i) / UDS() / UDS_DSC(diagnosticSessionType=0x01)
                save_log(pkt, True)
                resp = self.socket.sr1(pkt, timeout=0.2, verbose=0)
                if resp is not None:
                    save_log(resp, False)
                    print("Brute Success !!!")
                    print("Target Address:",hex(i))
                    flag = 1
                    self.target = i
                    break
            if flag == 0:
                print('Target Address Not Found !!!')  
        

    def do_dumpdids(self,args):
        """
        Read Data by DID
        -v: default show NRC
        Usage: 
            dumpdids f190,f193-f311,f413,f567 -v 
            dumpdids f190,f193-f311,f413,f567
        """

        verbos = False
        if '-v' in args:
            verbos = True
            args = args.split(' ')[0]

        if self.socket is None:
            print('Please connect first')
        elif self.target is None:
            print('Please Set target address manually')
        else:
            did_disc = {}
            output_list = []
            for part in args.split(','):
                if '-' in part:
                    start, end = part.split('-')
                    start = int(start,16)
                    end = int(end,16)
                    output_list.extend(range(start, end+1))
                else:
                    output_list.append(int(part,16))
            output_list = sorted(set(output_list))
            print('dids:')
            # try:
            doip = DoIP(payload_type=0x8001, source_address=self.source, target_address=self.target)
            for each in output_list:
            # 读DID
                data = hex(each)[2:].rjust(4,'0')
                resp = get_doip(self.socket, doip, '22'+data,verbos)
                if resp and resp[UDS].service == 0x62:
                    rcv_data =  binascii.b2a_hex(resp[Raw].load).decode('utf-8')
                    print(data,'\t',rcv_data)
                    did_disc[data] = rcv_data

            with open('dids.json','w') as f:
                json.dump(did_disc,f)

            print("\nAll DIDs are Saved in the dids.json file")
            print('path: ',os.getcwd()+os.sep+'dids.json')
    
    def do_set(self,args):
        '''
        Some data cannot be obtained automatically,
        You must manually set the value.
        Such as: target_address、source_address、key

        Usage: 
            set --target 0x0515 --source 0x0e80
            set -t 0x0515
            set -s 0e80
            set -k 58C63BDAC7ABA2C48B31C03BAB127865
            set --key 58C63BDAC7ABA2C48B31C03BAB127865
            ......
        '''
        args = args.split(' ')
        try:
            options = getopt.getopt(
                    args,'t:s:k:',
                    ['target=', 'source=','key='])
            for opt, arg in options[0]:
                if opt in ('-s', '--source'):
                    self.source = int(arg,16)
                elif opt in ('-t', '--target'):
                    self.target = int(arg,16)
                elif opt in ('-k', '--key'):
                    self.key = arg

        except getopt.GetoptError as msg:
            self.do_help('set')
            print("ERROR:", msg)
    
    def do_reset(self, args):
        '''
        ECU reset

        Usage: reset
        '''

        if self.socket is None:
            print('Please connect first')
        elif self.target is None:
            print('Please Set target address manually')
        else:
            pkt = DoIP(payload_type=0x8001, source_address=self.source, target_address=self.target) / UDS() / UDS_ER(resetType="hardReset")
            save_log(pkt, True)
            resp = self.socket.sr1(pkt, timeout=1, verbose=0)
            if resp:
                save_log(resp, True)
                if resp[UDS].service == 0x51:
                    print('ECU Reset Success')
                else:
                    print('ECU Reset Failed')
            else:
                print('ECU Reset Failed')

    def do_clear(self,args):
        '''
        clear socket and connect other ECU

        Usage: clear socket
        '''
        if 'socket' in args:
            self.socket.close()
            print("Clear Socket Success")


# '''
# 1003 2701 2702 --cmac
# 1002 2701 -n 10
# 1003 2701 2702aaaaaaaa 2702bbbbbbbb 2702cccccccc
# 1003 2701 2702 --cmac 3101aabb -f aa.cert
# 1003 1002 2ef190aabb
# 1003 1002 2e -f bb.json
# '''
    def do_send(self, args):

        '''
        send 1003 2701 2702 --cmac
        send 1002 2701 -n 10
        send 1003 2701 2702aaaaaaaa 2702bbbbbbbb 2702cccccccc
        send 1003 1002 2ef190aabb
        send 1003 2e -f dids.json
        '''
        if self.socket is None:
            print('Please connect first')
        elif args == '':
            print('Please input send Data')
        else:
            args = args.split(' ')
            doip = DoIP(payload_type=0x8001, source_address=self.source, target_address=self.target)
            
            cal_cmac = False
            random_seed = False
            pt31_flag = False
            pt2e_flag = False


            filename = None
            number = None
            

            if '--cmac' in args:
                cal_cmac = True
                args.pop(args.index('--cmac'))

            elif '-n' in args:
                random_seed = True
                number = int(args.pop(args.index('-n')+1),10)
                args.pop(args.index('-n'))

            elif '-f' in args:
                filename = args.pop(args.index('-f')+1)
                if '.json' in filename:
                    pt2e_flag = True
                else:
                    pt31_flag = True
                args.pop(args.index('-f'))

            elif '--cmac' in args and '-n' in args:
                print('Input Error')
            

            # print(cal_cmac,
            #     random_seed,
            #     pt31_flag,
            #     pt2e_flag,
            #     filename,
            #     number,
            #     args
            # )

            for each in args:
                if '3101' == each[:4] and pt31_flag:
                    #密钥罐装
                    pass

                elif '27' == each[:2] and int(each[2:4],10) % 2 == 1:
                    if random_seed:
                        tmp_seed = getseed(number, each, doip,self.socket)
                    else:
                        tmp_seed = getseed(1, each, doip,self.socket)
                    
                    if len(tmp_seed) == 1 and not random_seed:
                        self.seedlist = tmp_seed[0]

                    elif tmp_seed and random_seed:
                        f = open('seed.log','w')
                        for each in tmp_seed:
                            f.writelines(each+'\n')
                        f.close()
                        print('All the seeds have been saved in seed.log')
                        print('path: ',os.getcwd()+os.sep+'seed.log')
                    continue

                elif '27' == each[:2] and int(each[2:4],10) % 2 == 0 and cal_cmac and self.seedlist:
                    if self.key:
                        data = my_cmac(self.key,self.seedlist)
                        each = each + data
                        # print('cal_cmac succ',each)
                    else:
                        print('Please set -k first！！！')

                elif '2e' == each or '2E' == each and pt2e_flag:
                    print('')
                    pt2e(doip, self.socket, filename)
                    continue
                
                resp = get_doip(self.socket, doip, each)

    def do_routingActivation(self,args):
        '''
        Routing Activation Manually
        Usage: routingActivation
        '''
        if self.socket is None:
            print('Please connect first')
        else:
            data = DoIP(payload_type=0x5, activation_type=0,
                    source_address=self.source, reserved_oem=b"")
            save_log(data,True)
            resp = self.socket.sr1(
                data,
                verbose=False, timeout=1)
                
            if resp and resp.payload_type == 0x6 and \
                    resp.routing_activation_response == 0x10:
                self.target_address = 0 or \
                    resp.logical_address_doip_entity
                save_log(resp,False)
                log_automotive.info(
                    "Routing activation successful! Target address set to: 0x%x",
                    self.target_address)
                self.target = self.target_address
        
    def do_quit(self,args):
        '''
        Usage: quit [-h]

        Exit this application

        optional arguments:
        -h, --help  show this help message and exit
        '''
        logger.info('='*84)
        print('Recorded in: ',os.getcwd()+os.sep+'doip.log')
        if self.socket:
            self.socket.close()
            self.target = None
        return True

    def postcmd(self, stop, line):
        '''
        Usage: ctrl+z
        Exit this application
        '''
        if stop:
            return True
        return False