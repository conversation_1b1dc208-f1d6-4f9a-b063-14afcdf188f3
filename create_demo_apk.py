#!/usr/bin/env python3
"""
创建一个包含国密算法特征的演示APK文件
"""

import zipfile
import tempfile
import os
import struct
from pathlib import Path

# 导入我们的常量
import importlib.util
spec = importlib.util.spec_from_file_location("sm_detect", "SM detect.py")
sm_detect = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sm_detect)

SMCryptoConstants = sm_detect.SMCryptoConstants


def create_demo_dex_with_sm_constants():
    """创建包含SM算法常量的DEX文件"""
    constants = SMCryptoConstants()
    
    # 创建一个简单的DEX文件结构
    dex_header = b'dex\n035\x00'  # DEX魔数和版本
    dex_header += b'\x00' * 32    # 校验和等字段
    
    # 添加一些SM2椭圆曲线参数作为字符串常量
    sm2_constants = [
        constants.SM2_CURVE_P,
        constants.SM2_CURVE_A,
        constants.SM2_CURVE_B,
        constants.SM2_CURVE_N,
        constants.SM2_CURVE_GX,
        constants.SM2_CURVE_GY
    ]
    
    # 添加SM3常量
    sm3_constants = [
        hex(t)[2:].upper().zfill(8) for t in constants.SM3_T_ARRAY
    ] + [
        hex(iv)[2:].upper().zfill(8) for iv in constants.SM3_IV
    ]
    
    # 构建字符串池
    string_pool = b''
    for const in sm2_constants + sm3_constants:
        string_pool += len(const).to_bytes(1, 'little')  # 字符串长度
        string_pool += const.encode('utf-8')             # 字符串内容
        string_pool += b'\x00'                           # 结束符
    
    # 构建完整的DEX文件
    dex_content = dex_header + string_pool
    
    # 填充到合理的大小
    while len(dex_content) < 1024:
        dex_content += b'\x00'
    
    return dex_content


def create_demo_so_with_sm_constants():
    """创建包含SM算法常量的SO文件"""
    constants = SMCryptoConstants()

    # 创建一个简单但有效的ELF文件
    # 这里我们直接在数据中嵌入SM算法常量的十六进制表示

    # ELF文件头（64位，小端序）
    elf_header = bytearray(64)
    elf_header[0:4] = b'\x7fELF'  # ELF魔数
    elf_header[4] = 2             # 64位
    elf_header[5] = 1             # 小端序
    elf_header[6] = 1             # ELF版本
    elf_header[16:18] = struct.pack('<H', 3)  # 共享对象文件
    elf_header[18:20] = struct.pack('<H', 0x3e)  # x86-64架构
    elf_header[20:24] = struct.pack('<I', 1)  # ELF版本
    elf_header[32:40] = struct.pack('<Q', 64)  # 程序头偏移
    elf_header[40:48] = struct.pack('<Q', 0)   # 段头偏移
    elf_header[52:54] = struct.pack('<H', 64)  # ELF头大小
    elf_header[54:56] = struct.pack('<H', 56)  # 程序头大小
    elf_header[56:58] = struct.pack('<H', 0)   # 程序头数量
    elf_header[58:60] = struct.pack('<H', 64)  # 段头大小
    elf_header[60:62] = struct.pack('<H', 0)   # 段头数量
    elf_header[62:64] = struct.pack('<H', 0)   # 段头字符串表索引

    # 创建包含SM算法常量的数据段
    data_section = b''

    # 添加SM4的SBox（作为连续的字节序列）
    sm4_sbox_bytes = bytes(constants.SM4_SBOX)
    data_section += sm4_sbox_bytes

    # 添加一些标识字符串
    data_section += b'SM4_SBOX_DATA\x00'
    data_section += b'SM2_CRYPTO_LIB\x00'
    data_section += b'SM3_HASH_FUNC\x00'

    # 添加SM4的CK数组（以十六进制字符串形式）
    ck_hex_string = ''.join([hex(ck)[2:].upper().zfill(8) for ck in constants.SM4_CK])
    data_section += ck_hex_string.encode('ascii') + b'\x00'

    # 添加SM4的FK数组
    fk_hex_string = ''.join([hex(fk)[2:].upper().zfill(8) for fk in constants.SM4_FK])
    data_section += fk_hex_string.encode('ascii') + b'\x00'

    # 添加SM2椭圆曲线参数
    data_section += constants.SM2_CURVE_P.encode('ascii') + b'\x00'
    data_section += constants.SM2_CURVE_A.encode('ascii') + b'\x00'
    data_section += constants.SM2_CURVE_B.encode('ascii') + b'\x00'

    # 添加SM3常量
    for t_val in constants.SM3_T_ARRAY:
        t_hex = hex(t_val)[2:].upper().zfill(8)
        data_section += t_hex.encode('ascii') + b'\x00'

    for iv_val in constants.SM3_IV:
        iv_hex = hex(iv_val)[2:].upper().zfill(8)
        data_section += iv_hex.encode('ascii') + b'\x00'

    # 构建完整的SO文件
    so_content = bytes(elf_header) + data_section

    # 填充到合理的大小
    while len(so_content) < 4096:
        so_content += b'\x00'

    return so_content


def create_demo_apk():
    """创建演示APK文件"""
    demo_apk_path = "demo_sm_crypto.apk"
    
    with zipfile.ZipFile(demo_apk_path, 'w') as apk_zip:
        # 添加AndroidManifest.xml
        manifest_content = '''<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.demo.smcrypto"
    android:versionCode="1"
    android:versionName="1.0">
    
    <uses-sdk android:minSdkVersion="21" android:targetSdkVersion="30" />
    
    <application 
        android:label="SM Crypto Demo"
        android:icon="@mipmap/ic_launcher">
        
        <activity 
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- 模拟包含SM算法的服务 -->
        <service android:name=".SM2CryptoService" />
        <service android:name=".SM3HashService" />
        <service android:name=".SM4CipherService" />
        
    </application>
</manifest>'''
        apk_zip.writestr('AndroidManifest.xml', manifest_content.encode('utf-8'))
        
        # 添加包含SM常量的DEX文件
        dex_content = create_demo_dex_with_sm_constants()
        apk_zip.writestr('classes.dex', dex_content)
        
        # 添加包含SM常量的SO文件
        so_content = create_demo_so_with_sm_constants()
        apk_zip.writestr('lib/arm64-v8a/libsmcrypto.so', so_content)
        
        # 添加另一个SO文件（模拟OpenSSL变体）
        openssl_so = create_demo_so_with_sm_constants()
        apk_zip.writestr('lib/arm64-v8a/libopenssl_sm.so', openssl_so)
        
        # 添加资源文件
        apk_zip.writestr('res/values/strings.xml', '''<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">SM Crypto Demo</string>
    <string name="sm2_algorithm">SM2椭圆曲线算法</string>
    <string name="sm3_algorithm">SM3杂凑算法</string>
    <string name="sm4_algorithm">SM4分组密码算法</string>
</resources>'''.encode('utf-8'))
        
        # 添加一些干扰项（应该被过滤掉）
        apk_zip.writestr('assets/test_data.txt', '''
这是一个测试文件
包含一些干扰项：
- isM3U8File: 这是HLS协议相关的
- GSM2Network: 这是通信协议相关的
- lambda$test$SM3: 这是Lambda表达式
- 还有一些Base64编码的证书数据...
'''.encode('utf-8'))
    
    return demo_apk_path


def main():
    """主函数"""
    print("创建国密算法检测演示APK文件...")
    
    demo_apk = create_demo_apk()
    
    print(f"✓ 演示APK文件已创建: {demo_apk}")
    print(f"文件大小: {os.path.getsize(demo_apk)} 字节")
    
    print("\n演示APK包含的特征:")
    print("- SM2椭圆曲线参数（在DEX文件中）")
    print("- SM3算法常量（在DEX文件中）")
    print("- SM4算法常量（在SO文件中）")
    print("- 模拟的SM算法服务类名")
    print("- OpenSSL变体库文件")
    print("- 一些干扰项（用于测试过滤功能）")
    
    print(f"\n现在可以使用以下命令分析这个APK:")
    print(f'python "SM detect.py" {demo_apk}')
    print(f'python "SM detect.py" {demo_apk} -o report.json -v')


if __name__ == "__main__":
    main()
