{"apk_file": "Demo.apk", "analysis_summary": {"total_candidates": 170, "filtered_candidates": 197, "verified_algorithms": 197, "jni_connections": 0, "call_hierarchy_relations": 27}, "algorithms_found": {"SM4": {"count": 108, "types": ["native_constant", "native_function"], "confidence_levels": ["high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "high", "high", "high", "high"]}, "SM3": {"count": 26, "types": ["native_function"], "confidence_levels": ["medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium"]}, "SM2": {"count": 63, "types": ["native_constant", "native_function", "native_call"], "confidence_levels": ["high", "high", "high", "high", "high", "high", "high", "high", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium", "medium"]}}, "jni_analysis": {"connections_found": 0, "connections": []}, "native_call_hierarchy": {"relations_found": 27, "relations": [{"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_decryptPlain", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_encrypt", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_ccbSign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_compute_key", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_signData", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlain2", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_par_dig", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_verify", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlainWhiteBox", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2Sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_a1_3", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_verify", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_DH_key", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2SignNew", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_a4_10", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_sign_ex", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_get<PERSON><PERSON><PERSON>ey", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_b10", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "testSM2Sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keygen", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlain", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_decrypt", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_encryptPlain", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_ccbVerify", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_b1_9", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}]}, "detailed_results": [{"type": "native_constant", "file": "assets/arm64-v8a/libRiskStub00.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z10sm4_setkeyPmPh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "sm4_crypt_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z10sm4CalciRKm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z5sm4Ltm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z4sm4Fmmmmm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z7sm4Sboxh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z10encryptSM4P7_JNIEnvPhS1_P11_jbyteArrayiih", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z6do_sm4PhS_PciPiiib", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "sm4_crypt_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "_Z13sm4_one_roundPmPhS0_", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/arm64-v8a/libRiskStub00.so", "function": "sm4_init", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_constant", "file": "assets/armeabi-v7a/libRiskStub00.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z7sm4Sboxh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z5sm4Ltm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z4sm4Fmmmmm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z10sm4CalciRKm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z10sm4_setkeyPmPh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z13sm4_one_roundPmPhS0_", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "sm4_init", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "sm4_crypt_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "sm4_crypt_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z6do_sm4PhS_PciPiiib", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/armeabi-v7a/libRiskStub00.so", "function": "_Z10encryptSM4P7_JNIEnvPhS1_P11_jbyteArrayiih", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_constant", "file": "assets/x86/libRiskStub00.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z10sm4CalciRKm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z10sm4_setkeyPmPh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z13sm4_one_roundPmPhS0_", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z4sm4Fmmmmm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z5sm4Ltm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z7sm4Sboxh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "sm4_crypt_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "sm4_crypt_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "sm4_init", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z10encryptSM4P7_JNIEnvPhS1_P11_jbyteArrayiih", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86/libRiskStub00.so", "function": "_Z6do_sm4PhS_PciPiiib", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_constant", "file": "assets/x86_64/libRiskStub00.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z10sm4CalciRKm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z10sm4_setkeyPmPh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z13sm4_one_roundPmPhS0_", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z4sm4Fmmmmm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z5sm4Ltm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z7sm4Sboxh", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "sm4_crypt_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "sm4_crypt_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "sm4_init", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z10encryptSM4P7_JNIEnvPhS1_P11_jbyteArrayiih", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "assets/x86_64/libRiskStub00.so", "function": "_Z6do_sm4PhS_PciPiiib", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libsqlcipher.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "EVP_sm4_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "sm3_final", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "SM4_set_key", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "SM4_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "sm3_update", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "sm3_block_data_order", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "sm3_transform", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "EVP_sm3", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "EVP_sm4_ctr", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "EVP_sm4_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "sm3_init", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "SM4_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "EVP_sm4_cfb128", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libsqlcipher.so", "function": "EVP_sm4_ofb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libsqlcipher.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "EVP_sm4_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "sm3_final", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "SM4_set_key", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "SM4_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "sm3_update", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "sm3_block_data_order", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "sm3_transform", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "EVP_sm3", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "EVP_sm4_ctr", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "EVP_sm4_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "sm3_init", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "SM4_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "EVP_sm4_cfb128", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/armeabi-v7a/libsqlcipher.so", "function": "EVP_sm4_ofb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libEsafeexptp.so", "section": "raw_binary", "constant": "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC", "algorithm": "SM2", "location": "raw_binary_search", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在raw_binary段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libEsafeexptp.so", "section": "raw_binary", "constant": "28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93", "algorithm": "SM2", "location": "raw_binary_search", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在raw_binary段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libEsafeexptp.so", "section": "raw_binary", "constant": "32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7", "algorithm": "SM2", "location": "raw_binary_search", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在raw_binary段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libEsafeexptp.so", "section": "raw_binary", "constant": "BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0", "algorithm": "SM2", "location": "raw_binary_search", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在raw_binary段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libEsafeexptp.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libEsafeexptp.so", "section": ".data", "constant": "FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC", "algorithm": "SM2", "location": ".data_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在.data段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libEsafeexptp.so", "section": ".data", "constant": "28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93", "algorithm": "SM2", "location": ".data_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在.data段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libEsafeexptp.so", "section": ".data", "constant": "32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7", "algorithm": "SM2", "location": ".data_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在.data段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libEsafeexptp.so", "section": ".data", "constant": "BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0", "algorithm": "SM2", "location": ".data_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM2算法相关的native_constant，在.data段匹配到算法常量"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_update_GM", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_decryptPlain", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_SM4_ecb_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "_Z11SMS4EncryptPmmS_", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_sign", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_encrypt", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_ccbSign", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_LSM4_cbc_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "_Z11SMS4DecryptPmmS_", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_compute_key", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_hmac_GM", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm4EcbPkcs5x", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm4_setkey_dec", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_e", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM4_CBC_Decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_z", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_anydef", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_SM4_cbc_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_Sm4LocalEncrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_signData", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_LSM4_cbc_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlain2", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "LOCAL_CRYPT_IV_SM4", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm4_crypt_cbc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_par_dig", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_verify", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlainWhiteBox", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_hmac_update_GM", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "LOCAL_CRYPT_KEY_SM4", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2Sign", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_SM4_ecb_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm4_crypt_ecb", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm4localStoreDecrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_hmac_final", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "_Z10SMS4SetKeyPmm", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_Sm3Digest", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_final_GM", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM4_ECB_Decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_keyagreement_a1_3", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_WB_LSM4_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM2_verify", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM2_DH_key", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_LSM4_ecb_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_tranSM4Encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_GM", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm4localStoreEncrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2SignNew", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_SM4_cbc_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_Sm4LocalDecrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_WB_SM4_encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM4_ECB_Encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_keyagreement_a4_10", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM2_sign_ex", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm4CbcPkcs5", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_get<PERSON><PERSON><PERSON>ey", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_keyagreement_b10", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM2_sign", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_eCCB", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm4CbcPkcs5x", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm3SignData", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "_Z13SMS4Encrypt1Mv", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_init_GM", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "testSM2Sign", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_WB_LSM4_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_keygen", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm3_hmac_init", "algorithm": "SM3", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM3算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM4_CBC_Encrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm4_setkey_enc", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlain", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM4_ECB_BlOCK", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_decrypt", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_LSM4_ecb_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_tranSM4Decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_encryptPlain", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm4EcbPkcs5", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "SM2_sign_setup", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_ccbVerify", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "Bangcle_WB_SM4_decrypt", "algorithm": "SM4", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM4算法相关的native_function"}}, {"type": "native_function", "file": "lib/arm64-v8a/libEsafeexptp.so", "function": "sm2_keyagreement_b1_9", "algorithm": "SM2", "location": "function_name", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_function"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libDexHelper.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/arm64-v8a/libDexHelper-x86.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libDexHelper.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_constant", "file": "lib/armeabi-v7a/libDexHelper-x86.so", "section": ".rodata", "constant": "D690E9FECCE13DB716B614C228FB2C052B679A762ABE04C3AA441326498606999C4250F491EF987A33540B43EDCFAC62E4B31CA9C908E89580DF94FA758F3FA64707A7FCF37317BA83593C19E6854FA8686B81B27164DA8BF8EB0F4B70569D351E240E5E6358D1A225227C3B01217887D40046579FD327524C3602E7A0C4C89EEABF8AD240C738B5A3F7F2CEF96115A1E0AE5DA49B341A55AD933230F58CB1E31DF6E22E8266CA60C02923AB0D534E6FD5DB3745DEFD8E2F03FF6A726D6C5B518D1BAF92BBDDBC7F11D95C411F105AD80AC13188A5CD7BBD2D74D012B8E5B4B08969974A0C96777E65B9F109C56EC68418F07DEC3ADC4D2079EE5F3ED7CB3948", "algorithm": "SM4", "location": ".rodata_section", "verification": {"method": "static_analysis", "confidence": "high", "details": "发现SM4算法相关的native_constant，在.rodata段匹配到算法常量"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_decryptPlain", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_encrypt", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_ccbSign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_compute_key", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_signData", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlain2", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_par_dig", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_verify", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlainWhiteBox", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2Sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_a1_3", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_verify", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_DH_key", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2SignNew", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_a4_10", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_sign_ex", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_get<PERSON><PERSON><PERSON>ey", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_b10", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "SM2_sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "testSM2Sign", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keygen", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "Java_com_ccb_crypto_tp_tool_TpSafeUtils_sm2EncryptPlain", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_decrypt", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_encryptPlain", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_ccbVerify", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}, {"type": "native_call", "caller": "SM2_sign_setup", "callee": "sm2_keyagreement_b1_9", "file": "lib/arm64-v8a/libEsafeexptp.so", "algorithm": "SM2", "location": "native_hierarchy_inferred", "verification": {"method": "static_analysis", "confidence": "medium", "details": "发现SM2算法相关的native_call"}}]}