"""
RGB (<PERSON>, Green, Blue) tests for random number generators.
These tests focus on bit distribution and related properties.
"""

import math
import numpy as np
from typing import Dict, List, Optional
from collections import Counter

from generators.base_generator import BaseGenerator
from utils.statistics import StatisticsUtils


class RGBTests:
    """Implementation of RGB tests"""
    
    def __init__(self, generator: BaseGenerator):
        self.generator = generator
        self.stats = StatisticsUtils()
    
    def rgb_bitdist(self, tsamples: int = 100000, psamples: int = 100, ntup: int = 1) -> Dict:
        """
        Bit distribution test - tests the distribution of n-tuples of bits.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate bit sequence
            bits = self.generator.random_bits(tsamples * ntup)
            
            # Count n-tuples
            tuple_counts = Counter()
            for i in range(0, len(bits) - ntup + 1, ntup):
                tuple_val = 0
                for j in range(ntup):
                    tuple_val = (tuple_val << 1) | bits[i + j]
                tuple_counts[tuple_val] += 1
            
            # Expected frequency for each n-tuple
            num_tuples = len(bits) // ntup
            expected_freq = num_tuples / (2**ntup)
            
            # Chi-square test
            observed = [tuple_counts.get(i, 0) for i in range(2**ntup)]
            expected = [expected_freq] * (2**ntup)
            
            _, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'rgb_bitdist',
            'ntup': ntup,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def rgb_minimum_distance(self, tsamples: int = 10000, psamples: int = 1000, ntup: int = 2) -> Dict:
        """
        Minimum distance test - tests minimum distance between random points.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate random points in n-dimensional unit hypercube
            points = []
            for _ in range(tsamples):
                point = tuple(self.generator.random_float() for _ in range(ntup))
                points.append(point)
            
            # Find minimum distance
            min_distance = float('inf')
            for i in range(len(points)):
                for j in range(i+1, len(points)):
                    distance = 0.0
                    for k in range(ntup):
                        distance += (points[i][k] - points[j][k])**2
                    distance = math.sqrt(distance)
                    min_distance = min(min_distance, distance)
            
            # Test against expected distribution
            # For n-dimensional unit hypercube, minimum distance follows specific distribution
            if min_distance < float('inf'):
                # Simplified test using exponential distribution approximation
                lambda_param = tsamples**(1/ntup) / 2.0
                p_val = 1 - math.exp(-lambda_param * min_distance)
            else:
                p_val = 0.5
            
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'rgb_minimum_distance',
            'ntup': ntup,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def rgb_permutations(self, tsamples: int = 100000, psamples: int = 100, ntup: int = 2) -> Dict:
        """
        Permutations test - tests the frequency of permutations.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate sequences and count permutation patterns
            perm_counts = Counter()
            
            for _ in range(tsamples):
                # Generate n random values
                values = [self.generator.random_float() for _ in range(ntup)]
                
                # Convert to permutation
                sorted_indices = sorted(range(ntup), key=lambda i: values[i])
                perm = tuple(sorted_indices)
                perm_counts[perm] += 1
            
            # Expected frequency for each permutation
            expected_freq = tsamples / math.factorial(ntup)
            
            # Chi-square test
            num_perms = math.factorial(ntup)
            observed = [perm_counts.get(self._index_to_permutation(i, ntup), 0) for i in range(num_perms)]
            expected = [expected_freq] * num_perms
            
            _, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'rgb_permutations',
            'ntup': ntup,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def rgb_lagged_sum(self, tsamples: int = 1000000, psamples: int = 100, lag: int = 0) -> Dict:
        """
        Lagged sum test - tests correlations at different lags.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate sequence of random numbers
            sequence = [self.generator.random_float() for _ in range(tsamples + lag)]
            
            # Calculate lagged correlations
            if lag == 0:
                # Test for uniformity
                _, p_val = self.stats.kolmogorov_smirnov_test(sequence[:tsamples], 'uniform')
            else:
                # Calculate correlation between x[i] and x[i+lag]
                correlations = []
                for i in range(tsamples):
                    correlations.append(sequence[i] * sequence[i + lag])
                
                # Test if correlations are as expected for independent sequences
                expected_correlation = 0.25  # E[X*Y] for independent uniform [0,1] variables
                observed_correlation = np.mean(correlations)
                
                # Normal approximation
                variance = 1.0 / (12 * tsamples)  # Approximate variance
                if variance > 0:
                    z_score = (observed_correlation - expected_correlation) / math.sqrt(variance)
                    p_val = 2 * (1 - self.stats.normal_cdf(abs(z_score)))
                else:
                    p_val = 0.5
            
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'rgb_lagged_sum',
            'ntup': lag,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def rgb_kstest_test(self, tsamples: int = 10000, psamples: int = 1000) -> Dict:
        """
        Kolmogorov-Smirnov test for uniformity.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate random numbers
            numbers = [self.generator.random_float() for _ in range(tsamples)]
            
            # Kolmogorov-Smirnov test for uniform distribution
            _, p_val = self.stats.kolmogorov_smirnov_test(numbers, 'uniform')
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'rgb_kstest_test',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def _index_to_permutation(self, index: int, n: int) -> tuple:
        """Convert index to permutation (Lehmer code)"""
        perm = []
        available = list(range(n))
        
        for i in range(n):
            factorial = math.factorial(n - 1 - i)
            pos = index // factorial
            index %= factorial
            perm.append(available.pop(pos))
        
        return tuple(perm)
    
    def _combine_p_values(self, p_values: List[float]) -> float:
        """Combine multiple p-values using Fisher's method"""
        if not p_values:
            return 0.5
        
        # Fisher's combined probability test
        chi_sq = -2 * sum(math.log(max(p, 1e-10)) for p in p_values)
        df = 2 * len(p_values)
        
        # Convert to p-value
        from scipy.stats import chi2
        combined_p = 1 - chi2.cdf(chi_sq, df)
        
        return max(min(combined_p, 1.0), 0.0)
