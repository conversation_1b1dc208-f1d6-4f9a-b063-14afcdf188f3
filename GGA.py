def hex_string_to_binary(hex_string, output_file_path):
    # 将16进制字符串转换为字节对象
    binary_data = bytes.fromhex(hex_string)

    # 将字节对象写入二进制文件
    with open(output_file_path, 'wb') as output_file:
        output_file.write(binary_data)

# 例子
hex_string = "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"
print(len(hex_string))
output_file_path = "output1.der"
hex_string_to_binary(hex_string, output_file_path)
