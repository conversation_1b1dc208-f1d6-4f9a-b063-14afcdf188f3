import sys
import os
import random
import argparse
import socket
import time
import signal
from scapy.all import *
from construct import *
from colorama import init, Fore
from scapy.all import *
from scapy.layers.l2 import *
from scapy.layers.inet import *
import platform

# 全局变量用于优雅退出
running = True
sock = None

def signal_handler(signum, frame):
    """处理Ctrl+C信号"""
    global running, sock
    print(Fore.YELLOW + "\n\n[  中断  ] 检测到Ctrl+C，正在安全退出...")
    running = False
    if sock:
        try:
            sock.close()
            print(Fore.GREEN + "[  TCP  ] 连接已关闭")
        except:
            pass

def setup_signal_handlers():
    """设置信号处理器"""
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)

def establish_tcp_connection(target_ip, target_port):
    """建立TCP连接"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(5)  # 设置超时时间
        s.connect((target_ip, target_port))
        print(Fore.GREEN + f"[  TCP  ] 成功连接到 {target_ip}:{target_port}")
        return s
    except socket.error as msg:
        print(Fore.RED + f"[  TCP  ] 连接失败: {msg}")
        return None

def send_routing_activation(sock):
    """发送路由激活消息"""
    # DoIP路由激活消息: 02 FD 00 05 00 00 00 0B 0E 80 00 00 00 00 00 FF FF FF FF
    routing_activation = bytes([
        0x02, 0xFD,  # 协议版本和反向版本
        0x00, 0x05,  # 载荷类型 (路由激活请求)
        0x00, 0x00, 0x00, 0x0B,  # 载荷长度 (11字节)
        0x0E, 0x80,  # 源地址
        0x00, 0x00,  # 激活类型
        0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF  # 保留字段和OEM特定字段
    ])

    try:
        sock.send(routing_activation)
        print(Fore.GREEN + "[  DoIP  ] 路由激活消息已发送")

        # 等待响应
        response = sock.recv(1024)
        if response:
            print(Fore.CYAN + "[  DoIP  ] 收到响应:")
            print(" ".join([f"{b:02X}" for b in response]))
            return True
        else:
            print(Fore.YELLOW + "[  DoIP  ] 未收到响应")
            return False
    except socket.error as e:
        print(Fore.RED + f"[  DoIP  ] 发送路由激活失败: {e}")
        return False

def heartBeat(target_ip, target_port):
    """心跳检测"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(2)
    try:
        s.connect((target_ip, target_port))
        s.close()
        return True
    except socket.error:
        return False

def generate_fuzz_data(base_data, fuzz_type="random"):
    """生成模糊测试数据"""
    if platform.system() == "Linux":
        try:
            import pyradamsa
            rad = pyradamsa.Radamsa()
            return rad.fuzz(base_data)
        except ImportError:
            print(Fore.YELLOW + "[  FUZZ  ] pyradamsa未安装，使用随机模糊测试")

    # Windows或Linux无pyradamsa时使用随机模糊测试
    fuzzed_data = bytearray(base_data)

    if fuzz_type == "random":
        # 随机修改字节
        for _ in range(random.randint(1, 5)):
            if len(fuzzed_data) > 0:
                pos = random.randint(0, len(fuzzed_data) - 1)
                fuzzed_data[pos] = random.randint(0, 255)
    elif fuzz_type == "length":
        # 长度变异
        if random.choice([True, False]):
            # 增加长度
            fuzzed_data.extend(bytes([random.randint(0, 255) for _ in range(random.randint(1, 100))]))
        else:
            # 减少长度
            if len(fuzzed_data) > 1:
                fuzzed_data = fuzzed_data[:random.randint(1, len(fuzzed_data))]

    return bytes(fuzzed_data)

def parseArgs():
    parser = argparse.ArgumentParser(description='DoIP Fuzzer with TCP connection and routing activation')
    parser.add_argument('-t', '--target', required=True, type=str, help='The IPV4 address of target.')
    parser.add_argument('-p', '--port', required=False, type=int, default=13400, help='The port of target (default: 13400).')
    parser.add_argument('-c', '--count', required=False, type=int, default=1000, help='Number of fuzz test cases (default: 1000).')
    parser.add_argument('-d', '--delay', required=False, type=float, default=0.1, help='Delay between packets in seconds (default: 0.1).')
    parser.add_argument('-m', '--monitor', required=False, action='store_true', help='Monitor target availability.')
    args = parser.parse_args()
    return args

def create_doip_message(payload_type, data=b"", source_addr=0x0E80, target_addr=0x1000):
    """创建DoIP消息"""
    # 根据不同的payload类型计算长度
    if payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息类型
        payload_length = 4 + len(data)  # 源地址(2) + 目标地址(2) + 数据长度
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
            (source_addr >> 8) & 0xFF,  # 源地址
            source_addr & 0xFF,
            (target_addr >> 8) & 0xFF,  # 目标地址
            target_addr & 0xFF,
        ])
        return doip_header + data
    else:
        # 其他类型的DoIP消息
        payload_length = len(data)
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
        ])
        return doip_header + data

def generate_random_doip_payload():
    """生成随机DoIP载荷"""
    # DoIP载荷类型定义
    payload_types = {
        0x0000: "通用DoIP头否定应答",
        0x0001: "车辆识别请求",
        0x0002: "车辆识别请求与EID",
        0x0003: "车辆识别请求与VIN",
        0x0004: "车辆公告消息",
        0x0005: "路由激活请求",
        0x0006: "路由激活响应",
        0x0007: "存活检查请求",
        0x0008: "存活检查响应",
        0x4001: "DoIP实体状态请求",
        0x4002: "DoIP实体状态响应",
        0x4003: "诊断电源模式信息请求",
        0x4004: "诊断电源模式信息响应",
        0x8001: "诊断消息",
        0x8002: "诊断消息正面应答",
        0x8003: "诊断消息否定应答",
    }

    # 随机选择载荷类型
    payload_type = random.choice(list(payload_types.keys()))

    # 根据载荷类型生成相应的数据
    if payload_type == 0x0001:  # 车辆识别请求
        data = b""  # 无数据
    elif payload_type == 0x0002:  # 车辆识别请求与EID
        data = bytes([random.randint(0, 255) for _ in range(6)])  # 6字节EID
    elif payload_type == 0x0003:  # 车辆识别请求与VIN
        data = bytes([random.randint(0x30, 0x5A) for _ in range(17)])  # 17字节VIN
    elif payload_type == 0x0005:  # 路由激活请求
        data = bytes([
            random.randint(0, 255), random.randint(0, 255),  # 源地址
            random.randint(0, 255),  # 激活类型
            0x00, 0x00, 0x00, 0x00,  # 保留
            random.randint(0, 255), random.randint(0, 255),
            random.randint(0, 255), random.randint(0, 255)  # OEM特定
        ])
    elif payload_type == 0x0007:  # 存活检查请求
        data = b""  # 无数据
    elif payload_type == 0x4001:  # DoIP实体状态请求
        data = b""  # 无数据
    elif payload_type == 0x4003:  # 诊断电源模式信息请求
        data = b""  # 无数据
    elif payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息
        # 生成随机诊断数据
        diagnostic_services = [
            [0x10, 0x01],  # 诊断会话控制
            [0x10, 0x02],  # 编程会话
            [0x10, 0x03],  # 扩展诊断会话
            [0x11, 0x01],  # ECU复位 - 硬复位
            [0x11, 0x02],  # ECU复位 - 键开关复位
            [0x11, 0x03],  # ECU复位 - 软复位
            [0x22, 0xF1, 0x90],  # 读取数据标识符 - VIN
            [0x22, 0xF1, 0x86],  # 读取数据标识符 - ECU序列号
            [0x22, 0xF1, 0x87],  # 读取数据标识符 - 支持的功能单元
            [0x27, 0x01],  # 安全访问 - 请求种子
            [0x27, 0x02],  # 安全访问 - 发送密钥
            [0x3E, 0x00],  # 测试器存在
            [0x85, 0x01],  # 控制DTC设置 - 开启
            [0x85, 0x02],  # 控制DTC设置 - 关闭
        ]

        base_service = random.choice(diagnostic_services)
        # 添加随机数据
        extra_data = [random.randint(0, 255) for _ in range(random.randint(0, 10))]
        data = bytes(base_service + extra_data)
    else:
        # 其他类型生成随机数据
        data_length = random.randint(0, 50)
        data = bytes([random.randint(0, 255) for _ in range(data_length)])

    return payload_type, data

def main():
    global running, sock

    init()  # 初始化colorama
    setup_signal_handlers()  # 设置信号处理器

    args = parseArgs()
    target_ip = args.target
    target_port = args.port

    print(Fore.CYAN + "=" * 60)
    print(Fore.CYAN + "           DoIP 模糊测试工具")
    print(Fore.CYAN + "=" * 60)
    print(Fore.WHITE + f"目标地址: {target_ip}:{target_port}")
    print(Fore.WHITE + f"测试用例数量: {args.count}")
    print(Fore.WHITE + f"包间延迟: {args.delay}秒")
    print(Fore.YELLOW + f"提示: 按 Ctrl+C 可以安全退出程序")
    print(Fore.CYAN + "=" * 60)

    # 建立TCP连接
    sock = establish_tcp_connection(target_ip, target_port)
    if not sock:
        print(Fore.RED + "[  错误  ] 无法建立TCP连接，退出程序")
        sys.exit(1)

    # 发送路由激活消息
    if not send_routing_activation(sock):
        print(Fore.RED + "[  错误  ] 路由激活失败，但继续进行模糊测试")

    # 等待一段时间确保路由激活完成
    time.sleep(1)

    # 开始模糊测试
    print(Fore.GREEN + "\n[  开始  ] 开始DoIP模糊测试...")
    print(Fore.CYAN + "[  信息  ] 将随机发送不同类型的DoIP载荷")

    successful_tests = 0
    failed_tests = 0

    try:
        for packet_num in range(1, args.count + 1):
            # 检查是否需要退出
            if not running:
                print(Fore.YELLOW + f"[  中断  ] 在第 {packet_num} 个测试用例时退出")
                break

            try:
                # 生成随机DoIP载荷
                payload_type, base_data = generate_random_doip_payload()

                # 对载荷数据进行模糊测试
                fuzz_data = generate_fuzz_data(base_data,
                                             random.choice(["random", "length"]))

                # 创建DoIP消息
                doip_message = create_doip_message(
                    payload_type=payload_type,
                    data=fuzz_data,
                    source_addr=random.randint(0x0E00, 0x0EFF),  # 随机源地址
                    target_addr=random.randint(0x1000, 0x17FF)   # 随机目标地址
                )

                # 显示当前测试的载荷类型
                payload_names = {
                    0x0000: "通用否定应答", 0x0001: "车辆识别请求", 0x0002: "车辆识别+EID",
                    0x0003: "车辆识别+VIN", 0x0004: "车辆公告", 0x0005: "路由激活请求",
                    0x0006: "路由激活响应", 0x0007: "存活检查请求", 0x0008: "存活检查响应",
                    0x4001: "实体状态请求", 0x4002: "实体状态响应", 0x4003: "电源模式请求",
                    0x4004: "电源模式响应", 0x8001: "诊断消息", 0x8002: "诊断正面应答",
                    0x8003: "诊断否定应答"
                }
                payload_name = payload_names.get(payload_type, f"未知类型(0x{payload_type:04X})")

                print(Fore.BLUE + f"[  {packet_num:04d}  ] 发送载荷: {payload_name} (0x{payload_type:04X}), 数据长度: {len(fuzz_data)}")

                # 发送模糊测试数据
                if running:  # 再次检查运行状态
                    sock.send(doip_message)
                else:
                    break

                # 尝试接收响应
                sock.settimeout(0.5)
                try:
                    response = sock.recv(1024)
                    if response:
                        print(Fore.GREEN + f"[  {packet_num:04d}  ] 收到响应 ({len(response)} 字节)")
                    else:
                        print(Fore.YELLOW + f"[  {packet_num:04d}  ] 无响应")
                except socket.timeout:
                    print(Fore.YELLOW + f"[  {packet_num:04d}  ] 响应超时")
                except socket.error as e:
                    print(Fore.RED + f"[  {packet_num:04d}  ] 接收错误: {e}")
                    # 重新建立连接
                    if sock:
                        sock.close()
                    if running:  # 只有在程序仍在运行时才重连
                        sock = establish_tcp_connection(target_ip, target_port)
                        if not sock:
                            print(Fore.RED + "[  错误  ] 无法重新建立连接，退出")
                            break
                        send_routing_activation(sock)
                    failed_tests += 1
                    continue

                successful_tests += 1

                # 监控目标可用性
                if args.monitor and packet_num % 10 == 0 and running:
                    if not heartBeat(target_ip, target_port):
                        print(Fore.RED + f"[  警告  ] 目标 {target_ip}:{target_port} 可能不可用")

                # 延迟（可中断的延迟）
                if running:
                    delay_start = time.time()
                    while time.time() - delay_start < args.delay and running:
                        time.sleep(0.01)  # 小间隔检查运行状态

            except KeyboardInterrupt:
                # 这个异常现在由信号处理器处理
                break
            except Exception as e:
                print(Fore.RED + f"[  {packet_num:04d}  ] 测试异常: {e}")
                failed_tests += 1
                if not running:
                    break
                continue

    except KeyboardInterrupt:
        # 额外的KeyboardInterrupt处理（以防信号处理器未捕获）
        print(Fore.YELLOW + "\n[  中断  ] 程序被用户中断")
        running = False

    finally:
        # 关闭连接
        if sock:
            try:
                sock.close()
                print(Fore.GREEN + "[  TCP  ] 连接已安全关闭")
            except:
                pass

        # 打印统计信息
        print(Fore.CYAN + "\n" + "=" * 60)
        if running:
            print(Fore.CYAN + "           测试完成统计")
        else:
            print(Fore.CYAN + "           测试中断统计")
        print(Fore.CYAN + "=" * 60)
        print(Fore.GREEN + f"成功测试: {successful_tests}")
        print(Fore.RED + f"失败测试: {failed_tests}")
        print(Fore.WHITE + f"总计测试: {successful_tests + failed_tests}")
        if not running:
            print(Fore.YELLOW + f"状态: 用户中断退出")
        else:
            print(Fore.GREEN + f"状态: 正常完成")
        print(Fore.CYAN + "=" * 60)
        print(Fore.WHITE + "感谢使用DoIP模糊测试工具！")

if __name__ == "__main__":
    main()
