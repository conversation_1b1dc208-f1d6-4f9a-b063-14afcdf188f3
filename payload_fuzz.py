import sys
import os
import random
import argparse
from scapy.all import *
from construct import *
from colorama import init, Fore
from scapy.all import *
from scapy.layers.l2 import *
from scapy.layers.inet import *
import platform

def heartBeat():
    ip_port = (sys.argv[1], 30490)
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        s.connect(ip_port)
    except socket.error as msg:
        print("Couldnt connect with the socket-server: %s\n terminating program" % msg)
        print("Call the admin, plz!")
        sys.exit(1)

def parseArgs():
    parser = argparse.ArgumentParser(description='A simple doip fuzzer')
    parser.add_argument('-t', '--target',  required=True, type=str, help='The IPV4 address of target.')
    parser.add_argument('-d', '--dport',  required=True, type=str, help='The port of target.')
    parser.add_argument('-p', '--protocol', required=True, type=str, choices=['udp', 'tcp'], help='DOIP is base on udp or tcp. Choose one of them.')
    parser.add_argument('-m', '--monitor', required=False, type=str, choices=['true', 'false'], default='true', help='Make sure the port is alive.')
    args = parser.parse_args()
    return args

def main():
    args = parseArgs()
    port = int(args.dport) #13400
    targetIP = args.target 
    if args.protocol == 'udp':
        protocol = UDP(sport=13400, dport=port)
    else:
        protocol = TCP(sport=13400, dport=port)  
    # Create the Some/IP packet
    ip = IP(dst=targetIP)   
    doip = Struct(
        "protocol_version" / Int8ub,
        "inverse_version" / Int8ub,
        "payload_type" / Int16ub,
        "payload_length" / Int32ub,
        "source_address" / Int16ub,
        "target_address" / Int16ub,
        )
    
    packet_num = 1
    #if(packet_num == 1):
    while True:
        # Send the packet
        # "payload" / Bytes(lambda this: this.length),
        if platform.system() == "Windows":
            mydata = b'DOIP_data'
        if platform.system() == "Linux":
            import pyradamsa
            rad = pyradamsa.Radamsa()
            data = b'DOIP_data'
            mydata = rad.fuzz(data)
        # Create a SOME/IP packet
        
        doip_packet = doip.build(dict(protocol_version=0x2,
                   inverse_version=0xfd,
                   payload_type=0x8001,
                   payload_length=0x9,
                   source_address=0x1602,
                   target_address=0x0e80,
                   ))
        doip_packet += doip_packet + mydata
        packet = ip/protocol/doip_packet
        send(packet)
        hexdump(packet)
        if args.monitor == 'true':
            heartBeat()
        print(Fore.BLUE + "[  DO/IP  ] " + Fore.YELLOW + "模糊测试第 {} 条用例通过".format(packet_num))
        packet_num += 1

    
if __name__ == "__main__":
    main()














