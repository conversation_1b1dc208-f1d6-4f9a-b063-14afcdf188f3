import sys
import os
import random
import argparse
import socket
import time
from scapy.all import *
from construct import *
from colorama import init, Fore
from scapy.all import *
from scapy.layers.l2 import *
from scapy.layers.inet import *
import platform

def establish_tcp_connection(target_ip, target_port):
    """建立TCP连接"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(5)  # 设置超时时间
        s.connect((target_ip, target_port))
        print(Fore.GREEN + f"[  TCP  ] 成功连接到 {target_ip}:{target_port}")
        return s
    except socket.error as msg:
        print(Fore.RED + f"[  TCP  ] 连接失败: {msg}")
        return None

def send_routing_activation(sock):
    """发送路由激活消息"""
    # DoIP路由激活消息: 02 FD 00 05 00 00 00 0B 0E 80 00 00 00 00 00 FF FF FF FF
    routing_activation = bytes([
        0x02, 0xFD,  # 协议版本和反向版本
        0x00, 0x05,  # 载荷类型 (路由激活请求)
        0x00, 0x00, 0x00, 0x0B,  # 载荷长度 (11字节)
        0x0E, 0x80,  # 源地址
        0x00, 0x00,  # 激活类型
        0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF  # 保留字段和OEM特定字段
    ])

    try:
        sock.send(routing_activation)
        print(Fore.GREEN + "[  DoIP  ] 路由激活消息已发送")

        # 等待响应
        response = sock.recv(1024)
        if response:
            print(Fore.CYAN + "[  DoIP  ] 收到响应:")
            print(" ".join([f"{b:02X}" for b in response]))
            return True
        else:
            print(Fore.YELLOW + "[  DoIP  ] 未收到响应")
            return False
    except socket.error as e:
        print(Fore.RED + f"[  DoIP  ] 发送路由激活失败: {e}")
        return False

def heartBeat(target_ip, target_port):
    """心跳检测"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(2)
    try:
        s.connect((target_ip, target_port))
        s.close()
        return True
    except socket.error:
        return False

def generate_fuzz_data(base_data, fuzz_type="random"):
    """生成模糊测试数据"""
    if platform.system() == "Linux":
        try:
            import pyradamsa
            rad = pyradamsa.Radamsa()
            return rad.fuzz(base_data)
        except ImportError:
            print(Fore.YELLOW + "[  FUZZ  ] pyradamsa未安装，使用随机模糊测试")

    # Windows或Linux无pyradamsa时使用随机模糊测试
    fuzzed_data = bytearray(base_data)

    if fuzz_type == "random":
        # 随机修改字节
        for _ in range(random.randint(1, 5)):
            if len(fuzzed_data) > 0:
                pos = random.randint(0, len(fuzzed_data) - 1)
                fuzzed_data[pos] = random.randint(0, 255)
    elif fuzz_type == "length":
        # 长度变异
        if random.choice([True, False]):
            # 增加长度
            fuzzed_data.extend(bytes([random.randint(0, 255) for _ in range(random.randint(1, 100))]))
        else:
            # 减少长度
            if len(fuzzed_data) > 1:
                fuzzed_data = fuzzed_data[:random.randint(1, len(fuzzed_data))]

    return bytes(fuzzed_data)

def parseArgs():
    parser = argparse.ArgumentParser(description='DoIP Fuzzer with TCP connection and routing activation')
    parser.add_argument('-t', '--target', required=True, type=str, help='The IPV4 address of target.')
    parser.add_argument('-p', '--port', required=False, type=int, default=13400, help='The port of target (default: 13400).')
    parser.add_argument('-c', '--count', required=False, type=int, default=1000, help='Number of fuzz test cases (default: 1000).')
    parser.add_argument('-d', '--delay', required=False, type=float, default=0.1, help='Delay between packets in seconds (default: 0.1).')
    parser.add_argument('-m', '--monitor', required=False, action='store_true', help='Monitor target availability.')
    args = parser.parse_args()
    return args

def create_doip_diagnostic_message(source_addr=0x0E80, target_addr=0x1000, data=b""):
    """创建DoIP诊断消息"""
    payload_length = 4 + len(data)  # 源地址(2) + 目标地址(2) + 数据长度

    doip_header = bytes([
        0x02, 0xFD,  # 协议版本和反向版本
        0x80, 0x01,  # 载荷类型 (诊断消息)
        (payload_length >> 24) & 0xFF,  # 载荷长度 (高字节)
        (payload_length >> 16) & 0xFF,
        (payload_length >> 8) & 0xFF,
        payload_length & 0xFF,  # 载荷长度 (低字节)
        (source_addr >> 8) & 0xFF,  # 源地址 (高字节)
        source_addr & 0xFF,  # 源地址 (低字节)
        (target_addr >> 8) & 0xFF,  # 目标地址 (高字节)
        target_addr & 0xFF,  # 目标地址 (低字节)
    ])

    return doip_header + data

def main():
    init()  # 初始化colorama
    args = parseArgs()
    target_ip = args.target
    target_port = args.port

    print(Fore.CYAN + "=" * 60)
    print(Fore.CYAN + "           DoIP 模糊测试工具")
    print(Fore.CYAN + "=" * 60)
    print(Fore.WHITE + f"目标地址: {target_ip}:{target_port}")
    print(Fore.WHITE + f"测试用例数量: {args.count}")
    print(Fore.WHITE + f"包间延迟: {args.delay}秒")
    print(Fore.CYAN + "=" * 60)

    # 建立TCP连接
    sock = establish_tcp_connection(target_ip, target_port)
    if not sock:
        print(Fore.RED + "[  错误  ] 无法建立TCP连接，退出程序")
        sys.exit(1)

    # 发送路由激活消息
    if not send_routing_activation(sock):
        print(Fore.RED + "[  错误  ] 路由激活失败，但继续进行模糊测试")

    # 等待一段时间确保路由激活完成
    time.sleep(1)

    # 开始模糊测试
    print(Fore.GREEN + "\n[  开始  ] 开始DoIP模糊测试...")

    base_diagnostic_data = bytes([
        0x10, 0x01,  # 诊断会话控制服务
        0x22, 0xF1, 0x90,  # 读取数据标识符
        0x3E, 0x00,  # 测试器存在
    ])

    successful_tests = 0
    failed_tests = 0

    try:
        for packet_num in range(1, args.count + 1):
            try:
                # 生成模糊测试数据
                fuzz_data = generate_fuzz_data(base_diagnostic_data,
                                             random.choice(["random", "length"]))

                # 创建DoIP诊断消息
                doip_message = create_doip_diagnostic_message(
                    source_addr=0x0E80,
                    target_addr=0x1000,
                    data=fuzz_data
                )

                # 发送模糊测试数据
                sock.send(doip_message)

                # 尝试接收响应
                sock.settimeout(0.5)
                try:
                    response = sock.recv(1024)
                    if response:
                        print(Fore.GREEN + f"[  {packet_num:04d}  ] 收到响应 ({len(response)} 字节)")
                    else:
                        print(Fore.YELLOW + f"[  {packet_num:04d}  ] 无响应")
                except socket.timeout:
                    print(Fore.YELLOW + f"[  {packet_num:04d}  ] 响应超时")
                except socket.error as e:
                    print(Fore.RED + f"[  {packet_num:04d}  ] 接收错误: {e}")
                    # 重新建立连接
                    sock.close()
                    sock = establish_tcp_connection(target_ip, target_port)
                    if not sock:
                        print(Fore.RED + "[  错误  ] 无法重新建立连接，退出")
                        break
                    send_routing_activation(sock)
                    failed_tests += 1
                    continue

                successful_tests += 1

                # 监控目标可用性
                if args.monitor and packet_num % 10 == 0:
                    if not heartBeat(target_ip, target_port):
                        print(Fore.RED + f"[  警告  ] 目标 {target_ip}:{target_port} 可能不可用")

                # 延迟
                time.sleep(args.delay)

            except KeyboardInterrupt:
                print(Fore.YELLOW + "\n[  中断  ] 用户中断测试")
                break
            except Exception as e:
                print(Fore.RED + f"[  {packet_num:04d}  ] 测试异常: {e}")
                failed_tests += 1
                continue

    finally:
        # 关闭连接
        if sock:
            sock.close()

        # 打印统计信息
        print(Fore.CYAN + "\n" + "=" * 60)
        print(Fore.CYAN + "           测试完成统计")
        print(Fore.CYAN + "=" * 60)
        print(Fore.GREEN + f"成功测试: {successful_tests}")
        print(Fore.RED + f"失败测试: {failed_tests}")
        print(Fore.WHITE + f"总计测试: {successful_tests + failed_tests}")
        print(Fore.CYAN + "=" * 60)

    
if __name__ == "__main__":
    main()














