from scapy.all import *
import ipaddress
import requests
import json
import sys

file = sys.argv[1]


def get_ip(file):
    packets = rdpcap(file)
    ip = []
    length = len(packets)
    for i in range(length):
        if IP in packets[i]:
            if packets[i][IP].src[:2] == '1.':
                continue
            if packets[i][IP].src[:3] == '64.':
                continue
            if packets[i][IP].src[:4] == '128.':
                continue
            ip.append(packets[i][IP].src)
    ip = set(ip)
    return ip


def get_public_ip(ip):
    li = []
    for each in ip:
        ip = ipaddress.ip_address(each)
        if ip.is_private or ip.is_loopback or ip.is_multicast or ip.is_reserved:
            continue
        else:
            li.append(each)
    return li
    
def cross_border_addr(ip):
    query_api = "https://opendata.baidu.com/api.php?co=&resource_id=6006&oe=utf8&query="
    #homeland = ["中国","北京","天津","河北","山西","内蒙古","辽宁","吉林","黑龙江","上海","江苏","浙江","安徽","福建","江西","山东","河南","湖北","湖南","广东","广西","海南","重庆","四川","贵州","云南","西藏","陕西","甘肃","青海","宁夏","新疆"]
    response = requests.get(query_api+ip)
    location = json.loads(response.content.decode())["data"][0]["location"]
    return location.split(' ')[0]


ip = get_ip(file) 
ip = get_public_ip(ip)

for each in ip:
    result = cross_border_addr(each)
    if result:
        print("{:15}\t\t{}".format(each,result))