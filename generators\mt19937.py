"""
Mersenne Twister MT19937 random number generator implementation.
This is a high-quality pseudorandom number generator commonly used for testing.
"""

from .base_generator import BaseGenerator


class MT19937Generator(BaseGenerator):
    """Mersenne Twister MT19937 implementation"""
    
    # MT19937 constants
    N = 624
    M = 397
    MATRIX_A = 0x9908b0df
    UPPER_MASK = 0x80000000
    LOWER_MASK = 0x7fffffff
    TEMPERING_MASK_B = 0x9d2c5680
    TEMPERING_MASK_C = 0xefc60000
    
    def __init__(self, seed: int):
        """Initialize MT19937 with given seed"""
        super().__init__(seed)
    
    def _setup_generator(self, seed: int):
        """Initialize the MT19937 state array"""
        self.mt = [0] * self.N
        self.mti = self.N + 1
        
        self.mt[0] = seed & 0xffffffff
        for i in range(1, self.N):
            self.mt[i] = (1812433253 * (self.mt[i-1] ^ (self.mt[i-1] >> 30)) + i) & 0xffffffff
    
    def random_uint32(self) -> int:
        """Generate a random 32-bit unsigned integer using MT19937"""
        if self.mti >= self.N:
            self._generate_numbers()
        
        y = self.mt[self.mti]
        self.mti += 1
        
        # Tempering
        y ^= (y >> 11)
        y ^= (y << 7) & self.TEMPERING_MASK_B
        y ^= (y << 15) & self.TEMPERING_MASK_C
        y ^= (y >> 18)
        
        return y & 0xffffffff
    
    def _generate_numbers(self):
        """Generate the next N values from the series x_i"""
        for i in range(self.N):
            y = (self.mt[i] & self.UPPER_MASK) + (self.mt[(i + 1) % self.N] & self.LOWER_MASK)
            self.mt[i] = self.mt[(i + self.M) % self.N] ^ (y >> 1)
            if y % 2 != 0:
                self.mt[i] ^= self.MATRIX_A
        
        self.mti = 0
