#!/usr/bin/env python3
"""
UART嗅探器测试脚本
用于测试UARTSniffer类的各种功能
"""

import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from uart import UARTSniffer
from uart_config import get_config, validate_config, create_test_data


class TestUARTSniffer(unittest.TestCase):
    """UART嗅探器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            'port': 'COM_TEST',
            'baudrate': 115200,
            'idle_timeout': 0.1,
            'send_data': b'\x01\x02\x03\x04'
        }
    
    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        valid_config = get_config('debug')
        errors = validate_config(valid_config)
        self.assertEqual(len(errors), 0, "有效配置不应有错误")
        
        # 无效配置
        invalid_config = {'port': 'COM1'}  # 缺少必需字段
        errors = validate_config(invalid_config)
        self.assertGreater(len(errors), 0, "无效配置应有错误")
    
    def test_test_data_creation(self):
        """测试数据创建"""
        # 测试不同模式
        counter_data = create_test_data('counter', 4)
        self.assertEqual(len(counter_data), 4)
        self.assertEqual(counter_data, b'\x00\x01\x02\x03')
        
        alternating_data = create_test_data('alternating', 4)
        self.assertEqual(len(alternating_data), 4)
        self.assertEqual(alternating_data, b'\xaa\x55\xaa\x55')
        
        binary_data = create_test_data('binary', 4)
        self.assertEqual(len(binary_data), 4)
        self.assertEqual(binary_data, b'\x00\xff\x00\xff')
    
    @patch('serial.Serial')
    def test_sniffer_initialization(self, mock_serial):
        """测试嗅探器初始化"""
        sniffer = UARTSniffer(**self.test_config)
        
        self.assertEqual(sniffer.port, 'COM_TEST')
        self.assertEqual(sniffer.baudrate, 115200)
        self.assertEqual(sniffer.idle_timeout, 0.1)
        self.assertEqual(sniffer.send_data, b'\x01\x02\x03\x04')
        self.assertFalse(sniffer.is_running)
    
    @patch('serial.Serial')
    def test_sniffer_connect(self, mock_serial):
        """测试串口连接"""
        # 模拟成功连接
        mock_instance = Mock()
        mock_instance.is_open = True
        mock_serial.return_value = mock_instance
        
        sniffer = UARTSniffer(**self.test_config)
        result = sniffer.connect()
        
        self.assertTrue(result)
        mock_serial.assert_called_once()
    
    @patch('serial.Serial')
    def test_sniffer_connect_failure(self, mock_serial):
        """测试串口连接失败"""
        # 模拟连接失败
        import serial
        mock_serial.side_effect = serial.SerialException("连接失败")

        sniffer = UARTSniffer(**self.test_config)
        result = sniffer.connect()

        self.assertFalse(result)
    
    @patch('serial.Serial')
    def test_manual_send(self, mock_serial):
        """测试手动发送数据"""
        # 模拟串口
        mock_instance = Mock()
        mock_instance.is_open = True
        mock_serial.return_value = mock_instance
        
        sniffer = UARTSniffer(**self.test_config)
        sniffer.connect()
        
        # 测试发送默认数据
        result = sniffer.send_manual_data()
        self.assertTrue(result)
        mock_instance.write.assert_called_with(b'\x01\x02\x03\x04')
        
        # 测试发送自定义数据
        custom_data = b'\xAA\xBB\xCC\xDD'
        result = sniffer.send_manual_data(custom_data)
        self.assertTrue(result)
        mock_instance.write.assert_called_with(custom_data)


class TestUARTSnifferIntegration(unittest.TestCase):
    """UART嗅探器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            'port': 'COM_TEST',
            'baudrate': 115200,
            'idle_timeout': 0.05,  # 短超时用于快速测试
            'send_data': b'\xAA\xBB'
        }
    
    @patch('serial.Serial')
    def test_sniffing_lifecycle(self, mock_serial):
        """测试嗅探生命周期"""
        # 模拟串口
        mock_instance = Mock()
        mock_instance.is_open = True
        mock_instance.in_waiting = 0
        mock_serial.return_value = mock_instance
        
        sniffer = UARTSniffer(**self.test_config)
        
        # 启动嗅探
        result = sniffer.start_sniffing()
        self.assertTrue(result)
        self.assertTrue(sniffer.is_running)
        
        # 等待一小段时间
        time.sleep(0.1)
        
        # 停止嗅探
        sniffer.stop_sniffing()
        self.assertFalse(sniffer.is_running)
    
    @patch('serial.Serial')
    def test_data_reception(self, mock_serial):
        """测试数据接收"""
        # 模拟串口接收数据
        mock_instance = Mock()
        mock_instance.is_open = True
        mock_instance.in_waiting = 4
        mock_instance.read.return_value = b'\x12\x34\x56\x78'
        mock_serial.return_value = mock_instance
        
        sniffer = UARTSniffer(**self.test_config)
        sniffer.start_sniffing()
        
        # 等待数据处理
        time.sleep(0.1)
        
        # 检查数据队列
        self.assertFalse(sniffer.data_queue.empty())
        
        sniffer.stop_sniffing()


def run_mock_test():
    """运行模拟测试"""
    print("=== 运行模拟测试 ===")
    
    # 创建模拟嗅探器
    with patch('serial.Serial') as mock_serial:
        mock_instance = Mock()
        mock_instance.is_open = True
        mock_instance.in_waiting = 0
        mock_serial.return_value = mock_instance
        
        sniffer = UARTSniffer(
            port='COM_MOCK',
            baudrate=115200,
            idle_timeout=0.2,
            send_data=b'\xDE\xAD\xBE\xEF'
        )
        
        print("启动模拟嗅探器...")
        if sniffer.start_sniffing():
            print("嗅探器已启动")
            
            # 模拟数据接收
            def simulate_data():
                time.sleep(0.5)
                print("模拟接收数据...")
                mock_instance.in_waiting = 4
                mock_instance.read.return_value = b'\x11\x22\x33\x44'
                
                time.sleep(0.3)  # 等待空闲触发
                print("模拟空闲期结束")
            
            # 启动模拟线程
            sim_thread = threading.Thread(target=simulate_data, daemon=True)
            sim_thread.start()
            
            # 运行2秒
            time.sleep(2)
            
            print("停止模拟嗅探器...")
            sniffer.stop_sniffing()
            print("模拟测试完成")
        else:
            print("无法启动模拟嗅探器")


def run_performance_test():
    """运行性能测试"""
    print("=== 运行性能测试 ===")
    
    with patch('serial.Serial') as mock_serial:
        mock_instance = Mock()
        mock_instance.is_open = True
        mock_instance.in_waiting = 0
        mock_serial.return_value = mock_instance
        
        sniffer = UARTSniffer(
            port='COM_PERF',
            baudrate=921600,
            idle_timeout=0.01,  # 快速响应
            send_data=b'\xFF' * 100  # 大数据包
        )
        
        print("启动性能测试...")
        start_time = time.time()
        
        if sniffer.start_sniffing():
            # 模拟高频数据
            for i in range(100):
                mock_instance.in_waiting = 10
                mock_instance.read.return_value = bytes([i % 256] * 10)
                time.sleep(0.001)
            
            time.sleep(0.1)  # 等待处理完成
            sniffer.stop_sniffing()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"性能测试完成，耗时: {duration:.3f}秒")
            print(f"处理了100个数据包")
            print(f"平均处理速度: {100/duration:.1f} 包/秒")
        else:
            print("无法启动性能测试")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "unit":
            # 运行单元测试
            unittest.main(argv=[''], exit=False, verbosity=2)
        elif test_type == "mock":
            run_mock_test()
        elif test_type == "perf":
            run_performance_test()
        else:
            print("未知测试类型")
    else:
        print("UART嗅探器测试脚本")
        print("\n使用方法:")
        print("  python test_uart_sniffer.py unit   # 单元测试")
        print("  python test_uart_sniffer.py mock   # 模拟测试")
        print("  python test_uart_sniffer.py perf   # 性能测试")
        
        choice = input("\n选择测试类型 (unit/mock/perf): ").strip().lower()
        
        if choice == "unit":
            unittest.main(argv=[''], exit=False, verbosity=2)
        elif choice == "mock":
            run_mock_test()
        elif choice == "perf":
            run_performance_test()
        else:
            print("运行所有测试...")
            unittest.main(argv=[''], exit=False, verbosity=1)
            run_mock_test()
            run_performance_test()


if __name__ == "__main__":
    main()
