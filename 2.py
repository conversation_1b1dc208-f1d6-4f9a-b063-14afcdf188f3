import socket
import binascii
import time
from scapy.all import *
from scapy.sendrecv import sr1,srp
from scapy.all import ARP
from scapy.layers.inet import *
import binascii
from scapy.contrib.automotive.doip import DoIP
import os
f = open('recv.txt','w')


from scapy.main import load_contrib
load_contrib("automotive.doip")
load_contrib('automotive.uds')


while True:
    sk = socket.socket()
    sk.bind(('0.0.0.0', 13400))  # 绑定IP地址 端口号
    sk.listen(5)
    conn, addr = sk.accept()   # 等待客户端来连接
    try:
        while True:
            data = conn.recv(1024)

            if data == b'':
                conn.close()
                break
            doipdata = DoIP(data)  
            print(doipdata)
            recvdata = binascii.b2a_hex(data).decode('utf-8')
            f.write(recvdata+'\n')
            print(recvdata)
            if '1003' in recvdata:
                print('send....')
                conn.send(b'\x02\xfd\x80\x02\x00\x00\x00\x05\x15\x23\x0e\x80\x00')
                # time.sleep(50)
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x0a\x15\x23\x0e\x80\x50\x03\x00\x32\x01\xf4')
            if '1060' in recvdata:
                print('send....')
                conn.send(b'\x02\xfd\x80\x02\x00\x00\x00\x05\x15\x23\x0e\x80\x00')
                # time.sleep(50)
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x0a\x15\x23\x0e\x80\x50\x60\x00\x32\x01\xf4')
            elif '1001' in recvdata:
                print('send....')
                conn.send(b'\x02\xfd\x80\x02\x00\x00\x00\x05\x15\x23\x0e\x80\x00')
                time.sleep(0.1)
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x0a\x15\x23\x0e\x80\x50\x01\x00\x32\x01\xf4')

            elif '2701' in recvdata:
                print("send key......")
                hex_string = '02fd80010000001615230e806701'+os.urandom(16).hex()
                conn.send(b'\x02\xfd\x80\x02\x00\x00\x00\x05\x15\x23\x0e\x80\x27\x01')

                conn.send(binascii.a2b_hex(hex_string))
            elif '2702' in recvdata:
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x06\x15\x23\x0e\x80\x67\x02')

            # elif '2702' in recvdata:
            #     conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7F\x27\x35')


            elif '2703' in recvdata:
                print('send seed...')
                conn.send(binascii.a2b_hex('02fd80010000002c15230e806703179C0177938AA59D064EE92E78A2FC82'))
                
            elif '2704' in recvdata:
                print('send bypass..')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x06\x15\x23\x0e\x80\x67\x04')
                print('send by pass success')



            elif '2200' in recvdata:
                print('-----2200----')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x22\x7f')
            elif '2300' in recvdata:
                print('-----2300----')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x23\x11')
            elif '2400' in recvdata:
                print('-----2400----')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x24\x31')
            elif '2700' in recvdata:
                print('-----2700----')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x27\x7f')


            elif '1078' in recvdata:
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x10\x12')
                
            elif '1005' in recvdata:
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x10\x22')


            elif '2706' in recvdata:
                print('recvdata',recvdata)
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7F\x27\x22')

            elif '2705' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000002c15230e806705179C0177938AA59D064EE92E78A2FC82'))



            elif '1002' in recvdata:
                print('test 78')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x07\x15\x23\x0e\x80\x7f\x10\x78')
                conn.send(b'\x02\xfd\x80\x01\x00\x00\x00\x0a\x15\x23\x0e\x80\x50\x02\x00\x32\x00\xc8')
            
            # elif recvdata == '02fd0005000000070e800000000000':
            elif recvdata == '02fd00050000000b0e800000000000ffffffff':  #iso13400-2:2012
                conn.send(binascii.a2b_hex('02fd00060000000d0e8015231000000000'))
                
            #elif recvdata == '02fd00050000000b0e800000000000ffffffff':  #iso13400-2:2012
                #conn.send(binascii.a2b_hex('03fc00000000000100'))

            #elif recvdata == '03fc00050000000b0e800000000000ffffffff':  #iso13400-2:2012
                #conn.send(binascii.a2b_hex('03fc00060000000d0e8015231000000000'))

            elif '22f1a9' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000001615230e8062f1a9000003e8'))
            elif '22f1aa' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000001615230e8062f1aa1243ab78'))
            elif '22f1ab' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000001615230e807f2231'))

            elif '2fa8e40300' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000000915230e806fa8e60300'))
            
            elif '2fa8e502' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000000815230e806fa8e602'))
            elif '2fa8e602' in recvdata:
                conn.send(binascii.a2b_hex('02fd80010000000815230e807f2f11'))
            elif '2ef1a9' in recvdata:
                print('---2ef1a9')
                conn.send(binascii.a2b_hex('02fd8001000000e015230e806ef1a9'))
            elif '2ef1aa' in recvdata:
                print('---2ef1aa')
                conn.send(binascii.a2b_hex('02fd80010000000715230e807f2e22'))

            elif '1101' in recvdata:
                print('---ECU reset')
                conn.send(binascii.a2b_hex('02fd80010000000615230e805101'))
            
            elif '3101dd35018888' in recvdata:
                conn.send(binascii.a2b_hex('02FD80010000000900010E807101DD3500'))

    except Exception as e:
        print(e)
    finally:
        f.close()
        conn.close()
