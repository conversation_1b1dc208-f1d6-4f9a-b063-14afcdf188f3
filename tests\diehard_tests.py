"""
Diehard battery of tests for random number generators.
These are the classic tests developed by <PERSON>.
"""

import math
import numpy as np
from typing import Dict, List, Optional
from collections import Counter

from generators.base_generator import BaseGenerator
from utils.statistics import StatisticsUtils


class DieHardTests:
    """Implementation of Diehard battery of tests"""
    
    def __init__(self, generator: BaseGenerator):
        self.generator = generator
        self.stats = StatisticsUtils()
    
    def diehard_birthdays(self, tsamples: int = 100, psamples: int = 100) -> Dict:
        """
        Birthday spacings test.
        Tests the spacing between birthdays in a year.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate random birthdays
            birthdays = []
            for _ in range(tsamples):
                birthday = self.generator.random_uint32() % 365
                birthdays.append(birthday)
            
            # Sort and calculate spacings
            birthdays.sort()
            spacings = []
            for i in range(1, len(birthdays)):
                spacing = birthdays[i] - birthdays[i-1]
                spacings.append(spacing)
            
            # Test spacings for exponential distribution
            if spacings:
                # Convert to exponential test
                mean_spacing = np.mean(spacings)
                if mean_spacing > 0:
                    normalized = [s / mean_spacing for s in spacings]
                    ks_stat, p_val = self.stats.kolmogorov_smirnov_test(normalized, 'uniform')
                    p_values.append(p_val)
                else:
                    p_values.append(0.5)
            else:
                p_values.append(0.5)
        
        # Combine p-values using Fisher's method
        if p_values:
            combined_p = self._combine_p_values(p_values)
        else:
            combined_p = 0.5
        
        return {
            'name': 'diehard_birthdays',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def diehard_operm5(self, tsamples: int = 1000000, psamples: int = 100) -> Dict:
        """
        Overlapping permutations test.
        Tests overlapping 5-permutations.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate sequence and count overlapping permutations
            sequence = []
            for _ in range(tsamples + 4):  # Need extra for overlapping
                sequence.append(self.generator.random_uint32() % 5)
            
            # Count permutation patterns
            perm_counts = Counter()
            for i in range(tsamples):
                perm = tuple(sequence[i:i+5])
                # Convert to permutation rank
                rank = self._permutation_rank(perm)
                perm_counts[rank] += 1
            
            # Expected frequency for each permutation
            expected_freq = tsamples / 120  # 5! = 120 permutations
            
            # Chi-square test
            observed = [perm_counts.get(i, 0) for i in range(120)]
            expected = [expected_freq] * 120
            
            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'diehard_operm5',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def diehard_rank_32x32(self, tsamples: int = 40000, psamples: int = 100) -> Dict:
        """
        Binary rank test for 32x32 matrices.
        """
        p_values = []
        
        for _ in range(psamples):
            rank_counts = Counter()
            
            for _ in range(tsamples):
                # Generate 32x32 binary matrix
                matrix = np.zeros((32, 32), dtype=int)
                for i in range(32):
                    for j in range(32):
                        matrix[i, j] = self.generator.random_uint32() & 1
                
                # Calculate rank over GF(2)
                rank = self._binary_rank(matrix)
                rank_counts[rank] += 1
            
            # Expected probabilities for different ranks
            # These are theoretical values for 32x32 binary matrices
            expected_probs = self._get_binary_rank_probabilities(32)
            expected = [prob * tsamples for prob in expected_probs]
            observed = [rank_counts.get(i, 0) for i in range(len(expected_probs))]
            
            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'diehard_rank_32x32',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def diehard_rank_6x8(self, tsamples: int = 100000, psamples: int = 100) -> Dict:
        """
        Binary rank test for 6x8 matrices.
        """
        p_values = []
        
        for _ in range(psamples):
            rank_counts = Counter()
            
            for _ in range(tsamples):
                # Generate 6x8 binary matrix
                matrix = np.zeros((6, 8), dtype=int)
                for i in range(6):
                    for j in range(8):
                        matrix[i, j] = self.generator.random_uint32() & 1
                
                # Calculate rank over GF(2)
                rank = self._binary_rank(matrix)
                rank_counts[rank] += 1
            
            # Expected probabilities for 6x8 matrices
            expected_probs = self._get_binary_rank_probabilities(6, 8)
            expected = [prob * tsamples for prob in expected_probs]
            observed = [rank_counts.get(i, 0) for i in range(len(expected_probs))]
            
            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'diehard_rank_6x8',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def diehard_bitstream(self, tsamples: int = 2097152, psamples: int = 100) -> Dict:
        """
        Bitstream test.
        Tests overlapping 20-bit words.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate bit stream
            bits = self.generator.random_bits(tsamples)
            
            # Count overlapping 20-bit words
            word_counts = Counter()
            for i in range(len(bits) - 19):
                word = 0
                for j in range(20):
                    word = (word << 1) | bits[i + j]
                word_counts[word] += 1
            
            # Expected frequency
            num_words = len(bits) - 19
            expected_freq = num_words / (2**20)
            
            # Chi-square test (sample only some words due to large space)
            sample_size = min(1000, len(word_counts))
            observed = list(word_counts.values())[:sample_size]
            expected = [expected_freq] * len(observed)
            
            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'diehard_bitstream',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def _permutation_rank(self, perm: tuple) -> int:
        """Calculate the lexicographic rank of a permutation"""
        n = len(perm)
        rank = 0
        elements = list(range(n))
        
        for i in range(n):
            pos = elements.index(perm[i])
            rank += pos * math.factorial(n - 1 - i)
            elements.remove(perm[i])
        
        return rank
    
    def _binary_rank(self, matrix: np.ndarray) -> int:
        """Calculate binary rank of matrix over GF(2)"""
        m, n = matrix.shape
        rank = 0
        
        # Gaussian elimination over GF(2)
        for col in range(min(m, n)):
            # Find pivot
            pivot_row = None
            for row in range(rank, m):
                if matrix[row, col] == 1:
                    pivot_row = row
                    break
            
            if pivot_row is None:
                continue
            
            # Swap rows
            if pivot_row != rank:
                matrix[[rank, pivot_row]] = matrix[[pivot_row, rank]]
            
            # Eliminate
            for row in range(m):
                if row != rank and matrix[row, col] == 1:
                    matrix[row] ^= matrix[rank]
            
            rank += 1
        
        return rank
    
    def _get_binary_rank_probabilities(self, m: int, n: int = None) -> List[float]:
        """Get theoretical probabilities for binary matrix ranks"""
        if n is None:
            n = m
        
        # Simplified probabilities (actual calculation is complex)
        max_rank = min(m, n)
        probs = []
        
        for r in range(max_rank + 1):
            if r == max_rank:
                prob = 0.2888  # Approximate for full rank
            elif r == max_rank - 1:
                prob = 0.5776
            elif r == max_rank - 2:
                prob = 0.1283
            else:
                prob = 0.0053
            probs.append(prob)
        
        # Normalize
        total = sum(probs)
        return [p / total for p in probs]
    
    def diehard_opso(self, tsamples: int = 2097152, psamples: int = 100) -> Dict:
        """
        Overlapping-pairs-sparse-occupancy (OPSO) test.
        """
        p_values = []

        for _ in range(psamples):
            # Generate pairs and count sparse occupancy
            pairs = []
            for _ in range(tsamples):
                x = self.generator.random_uint32() % 1024
                y = self.generator.random_uint32() % 1024
                pairs.append((x, y))

            # Count occupied cells in 1024x1024 grid
            occupied = set(pairs)
            num_occupied = len(occupied)

            # Expected number of occupied cells
            expected = 1024 * 1024 * (1 - math.exp(-tsamples / (1024 * 1024)))

            # Normal approximation
            variance = 1024 * 1024 * math.exp(-tsamples / (1024 * 1024)) * (1 - math.exp(-tsamples / (1024 * 1024)))

            if variance > 0:
                z_score = (num_occupied - expected) / math.sqrt(variance)
                p_val = 2 * (1 - self.stats.normal_cdf(abs(z_score)))
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_opso',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_oqso(self, tsamples: int = 2097152, psamples: int = 100) -> Dict:
        """
        Overlapping-quadruples-sparse-occupancy (OQSO) test.
        """
        p_values = []

        for _ in range(psamples):
            # Generate quadruples
            quadruples = []
            for _ in range(tsamples):
                quad = tuple(self.generator.random_uint32() % 32 for _ in range(4))
                quadruples.append(quad)

            # Count occupied cells
            occupied = set(quadruples)
            num_occupied = len(occupied)

            # Expected number (32^4 = 1048576 possible quadruples)
            total_cells = 32**4
            expected = total_cells * (1 - math.exp(-tsamples / total_cells))

            # Normal approximation
            variance = total_cells * math.exp(-tsamples / total_cells) * (1 - math.exp(-tsamples / total_cells))

            if variance > 0:
                z_score = (num_occupied - expected) / math.sqrt(variance)
                p_val = 2 * (1 - self.stats.normal_cdf(abs(z_score)))
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_oqso',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_dna(self, tsamples: int = 2097152, psamples: int = 100) -> Dict:
        """
        DNA test - tests overlapping 10-letter words from 4-letter alphabet.
        """
        p_values = []

        for _ in range(psamples):
            # Generate DNA sequence (4-letter alphabet: A, C, G, T)
            dna_words = []
            for _ in range(tsamples):
                word = ""
                for _ in range(10):
                    letter = self.generator.random_uint32() % 4
                    word += "ACGT"[letter]
                dna_words.append(word)

            # Count word frequencies
            word_counts = Counter(dna_words)

            # Expected frequency
            expected_freq = tsamples / (4**10)

            # Chi-square test on sample of words
            sample_size = min(1000, len(word_counts))
            observed = list(word_counts.values())[:sample_size]
            expected = [expected_freq] * len(observed)

            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_dna',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_count_1s_str(self, tsamples: int = 256000, psamples: int = 100) -> Dict:
        """
        Count the 1s in a stream of bytes test.
        """
        p_values = []

        for _ in range(psamples):
            # Generate bytes and count 1s in each
            counts = []
            for _ in range(tsamples):
                byte_val = self.generator.random_uint32() & 0xFF
                count_1s = bin(byte_val).count('1')
                counts.append(count_1s)

            # Expected distribution (binomial with n=8, p=0.5)
            expected_probs = [self.stats.binomial_probability(8, k, 0.5) for k in range(9)]
            expected = [prob * tsamples for prob in expected_probs]

            # Observed frequencies
            observed = [0] * 9
            for count in counts:
                observed[count] += 1

            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_count_1s_str',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_count_1s_byt(self, tsamples: int = 256000, psamples: int = 100) -> Dict:
        """
        Count the 1s in specific bytes test.
        """
        p_values = []

        for _ in range(psamples):
            # Generate 32-bit words and count 1s in specific byte positions
            counts = []
            for _ in range(tsamples):
                word = self.generator.random_uint32()
                # Count 1s in each byte
                for byte_pos in range(4):
                    byte_val = (word >> (8 * byte_pos)) & 0xFF
                    count_1s = bin(byte_val).count('1')
                    counts.append(count_1s)

            # Expected distribution
            expected_probs = [self.stats.binomial_probability(8, k, 0.5) for k in range(9)]
            expected = [prob * len(counts) for prob in expected_probs]

            # Observed frequencies
            observed = [0] * 9
            for count in counts:
                observed[count] += 1

            chi_sq, p_val = self.stats.chi_square_test(observed, expected)
            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_count_1s_byt',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def _combine_p_values(self, p_values: List[float]) -> float:
        """Combine multiple p-values using Fisher's method"""
        if not p_values:
            return 0.5

        # Fisher's combined probability test
        chi_sq = -2 * sum(math.log(max(p, 1e-10)) for p in p_values)
        df = 2 * len(p_values)

        # Convert to p-value
        from scipy.stats import chi2
        combined_p = 1 - chi2.cdf(chi_sq, df)

        return max(min(combined_p, 1.0), 0.0)

    def diehard_parking_lot(self, tsamples: int = 12000, psamples: int = 100) -> Dict:
        """
        Parking lot test - tests random parking in a square.
        """
        p_values = []

        for _ in range(psamples):
            # Try to park cars randomly in a 100x100 square
            parked_cars = []
            successful_parks = 0

            for _ in range(tsamples):
                # Random position and orientation
                x = self.generator.random_float() * 100
                y = self.generator.random_float() * 100
                angle = self.generator.random_float() * 2 * math.pi

                # Car dimensions (simplified)
                car_length = 1.0
                car_width = 1.0

                # Check if car can be parked (simplified collision detection)
                can_park = True
                for parked_x, parked_y, parked_angle in parked_cars:
                    distance = math.sqrt((x - parked_x)**2 + (y - parked_y)**2)
                    if distance < 2.0:  # Simplified collision check
                        can_park = False
                        break

                if can_park:
                    parked_cars.append((x, y, angle))
                    successful_parks += 1

            # Expected number of successful parks (theoretical)
            expected_parks = tsamples * 0.7  # Approximate expected value

            # Normal approximation
            variance = tsamples * 0.7 * 0.3
            if variance > 0:
                z_score = (successful_parks - expected_parks) / math.sqrt(variance)
                p_val = 2 * (1 - self.stats.normal_cdf(abs(z_score)))
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_parking_lot',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_2dsphere(self, tsamples: int = 8000, psamples: int = 100) -> Dict:
        """
        2D sphere test - tests minimum distance between random points on a sphere.
        """
        p_values = []

        for _ in range(psamples):
            # Generate random points on unit sphere
            points = []
            for _ in range(tsamples):
                # Generate point on unit sphere using normal distribution
                x = self.generator.random_float() - 0.5
                y = self.generator.random_float() - 0.5
                z = self.generator.random_float() - 0.5

                # Normalize to unit sphere
                norm = math.sqrt(x*x + y*y + z*z)
                if norm > 0:
                    points.append((x/norm, y/norm, z/norm))

            # Find minimum distance
            min_distance = float('inf')
            for i in range(len(points)):
                for j in range(i+1, len(points)):
                    x1, y1, z1 = points[i]
                    x2, y2, z2 = points[j]
                    distance = math.sqrt((x1-x2)**2 + (y1-y2)**2 + (z1-z2)**2)
                    min_distance = min(min_distance, distance)

            # Test against expected distribution
            # For points on sphere, minimum distance follows specific distribution
            if min_distance < float('inf'):
                # Simplified test - use exponential distribution approximation
                lambda_param = tsamples / 4.0
                p_val = 1 - math.exp(-lambda_param * min_distance)
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_2dsphere',
            'ntup': 2,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_3dsphere(self, tsamples: int = 4000, psamples: int = 100) -> Dict:
        """
        3D sphere test - similar to 2D but in 3D space.
        """
        p_values = []

        for _ in range(psamples):
            # Generate random points in 3D unit sphere
            points = []
            for _ in range(tsamples):
                # Generate point inside unit sphere
                while True:
                    x = 2 * self.generator.random_float() - 1
                    y = 2 * self.generator.random_float() - 1
                    z = 2 * self.generator.random_float() - 1

                    if x*x + y*y + z*z <= 1.0:
                        points.append((x, y, z))
                        break

            # Find minimum distance
            min_distance = float('inf')
            for i in range(len(points)):
                for j in range(i+1, len(points)):
                    x1, y1, z1 = points[i]
                    x2, y2, z2 = points[j]
                    distance = math.sqrt((x1-x2)**2 + (y1-y2)**2 + (z1-z2)**2)
                    min_distance = min(min_distance, distance)

            # Test against expected distribution
            if min_distance < float('inf'):
                # Simplified test
                lambda_param = tsamples / 6.0
                p_val = 1 - math.exp(-lambda_param * min_distance)
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_3dsphere',
            'ntup': 3,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_squeeze(self, tsamples: int = 100000, psamples: int = 100) -> Dict:
        """
        Squeeze test - tests the squeeze operation on random integers.
        """
        p_values = []

        for _ in range(psamples):
            squeeze_counts = []

            for _ in range(tsamples):
                # Start with random 32-bit integer
                k = self.generator.random_uint32()
                j = self.generator.random_uint32()

                # Count iterations until squeeze converges
                count = 0
                while k != j and count < 100:  # Limit iterations
                    if k > j:
                        k, j = j, k
                    j = j - k
                    count += 1

                squeeze_counts.append(count)

            # Test distribution of squeeze counts
            # Expected to follow geometric-like distribution
            max_count = max(squeeze_counts) if squeeze_counts else 0
            observed = [0] * (max_count + 1)
            for count in squeeze_counts:
                observed[count] += 1

            # Simplified test - check if distribution is reasonable
            if len(observed) > 1:
                # Use chi-square test with uniform expected distribution
                expected = [tsamples / len(observed)] * len(observed)
                _, p_val = self.stats.chi_square_test(observed, expected)
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_squeeze',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_sums(self, tsamples: int = 100, psamples: int = 100) -> Dict:
        """
        Sums test - tests sums of successive floating point numbers.
        """
        p_values = []

        for _ in range(psamples):
            sums = []

            for _ in range(tsamples):
                # Generate sequence of random floats and sum them
                sequence_sum = 0.0
                for _ in range(100):  # Sum 100 random floats
                    sequence_sum += self.generator.random_float()
                sums.append(sequence_sum)

            # Test if sums follow normal distribution
            # Expected: mean = 50, variance = 100/12 ≈ 8.33
            expected_mean = 50.0
            expected_std = math.sqrt(100.0 / 12.0)

            # Normalize sums
            normalized = [(s - expected_mean) / expected_std for s in sums]

            # Kolmogorov-Smirnov test for normality
            _, p_val = self.stats.kolmogorov_smirnov_test(normalized, 'normal')
            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_sums',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_runs(self, tsamples: int = 100000, psamples: int = 100) -> Dict:
        """
        Runs test - tests runs of consecutive identical values.
        """
        p_values = []

        for _ in range(psamples):
            # Generate binary sequence
            sequence = []
            for _ in range(tsamples):
                bit = self.generator.random_uint32() & 1
                sequence.append(bit)

            # Perform runs test
            _, p_val = self.stats.runs_test(sequence)
            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_runs',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }

    def diehard_craps(self, tsamples: int = 200000, psamples: int = 100) -> Dict:
        """
        Craps test - simulates the game of craps.
        """
        p_values = []

        for _ in range(psamples):
            wins = 0

            for _ in range(tsamples):
                # Play one game of craps
                # First roll
                die1 = (self.generator.random_uint32() % 6) + 1
                die2 = (self.generator.random_uint32() % 6) + 1
                first_roll = die1 + die2

                if first_roll in [7, 11]:
                    wins += 1  # Win on first roll
                elif first_roll in [2, 3, 12]:
                    pass  # Lose on first roll
                else:
                    # Point established, keep rolling
                    point = first_roll
                    while True:
                        die1 = (self.generator.random_uint32() % 6) + 1
                        die2 = (self.generator.random_uint32() % 6) + 1
                        roll = die1 + die2

                        if roll == point:
                            wins += 1  # Win by making point
                            break
                        elif roll == 7:
                            break  # Lose by rolling 7

            # Expected win probability in craps is approximately 0.4929
            expected_wins = tsamples * 0.4929
            variance = tsamples * 0.4929 * (1 - 0.4929)

            if variance > 0:
                z_score = (wins - expected_wins) / math.sqrt(variance)
                p_val = 2 * (1 - self.stats.normal_cdf(abs(z_score)))
            else:
                p_val = 0.5

            p_values.append(p_val)

        combined_p = self._combine_p_values(p_values)

        return {
            'name': 'diehard_craps',
            'ntup': 0,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
