import socket
import time
import binascii
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import cmac
from cryptography.hazmat.backends import default_backend

socket = socket.socket()
socket.connect(('*************', 13400))
active = 0

def seed_to_key(seed):
    key = bytes.fromhex('8dd6887134c2b5758e215f14cec669d1')
    c = cmac.CMAC(algorithms.AES(key), backend=default_backend())
    c.update(bytes.fromhex(seed))
    mac = c.finalize()
    return mac

print(seed_to_key("27f5dc12").hex())


def format(data):
    formatted_string = " ".join([data[i:i+2] for i in range(0, len(data), 2)])
    return formatted_string

def send_and_rcv(data):
    global active
    response2 = None
    socket.send(binascii.a2b_hex(data))
    response = socket.recv(1024)
    response = binascii.b2a_hex(response).decode('utf-8')
    
    if '232408048' in data:
        print('aaaaa')
        response2 = socket.recv(1024)
        response2 = binascii.b2a_hex(response2).decode('utf-8')
    if active == 0:
        print('发送：',format(data))
        print('路由激活成功：',format(response))
        active = 1
    else:
        print('发送：',format(data[24:]))
        print('接收：',format(response[24:]))
        if response2:
            print('接收：',format(response2))
        if '62d888' in response[24:]:
            bytes_object = bytes.fromhex(response[30:])
            ascii_string = bytes_object.decode("ascii")
            print(ascii_string)
    return response

data = '02fd0005000000070e800000000000'
send_and_rcv(data)

# 过27服务
sessiondata = '02FD8001000000060E8000031003'
send_and_rcv(sessiondata)

sessiondata = '02FD8001000000060E8000031002'
send_and_rcv(sessiondata)

diddata = '02FD8001000000070E80000322d888'
send_and_rcv(diddata)

sessiondata = '02FD80010000000c0E8000032324080480000911'
response = send_and_rcv(sessiondata)

# for i in range(0x1000,1,-1):
#     memorySize = hex(i)[2:].rjust(4,'0')
#     sessiondata = '02FD80010000000c0E800003232408048000'+memorySize
#     response = send_and_rcv(sessiondata)
#     if len(response) > 30:
#         break




securitydata = '02FD8001000000060E8000032701'
response = send_and_rcv(securitydata)
seed = response.split('6701')[1]
print()
print('Seed: ',seed)

# byte_seed = bytes.fromhex(seed)
# result = xor_bytes(byte_seed, 0x14)
# key = result.hex()

# print("key：", key)
# print()

# datalength = 6 + len(key)//2

# datalength = hex(datalength)[2:].rjust(2,'0')

# keydata = '02FD8001000000' + datalength + '0E8000012702'+ key  
# send_and_rcv(keydata)

# diddata = '02FD8001000000080E800001310155bc'
# send_and_rcv(diddata)
socket.close()