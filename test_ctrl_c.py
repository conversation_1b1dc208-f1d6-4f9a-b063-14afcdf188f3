#!/usr/bin/env python3
"""
测试Ctrl+C功能的简单脚本
"""

import sys
import os
import signal
import time
from colorama import init, Fore

# 全局变量
running = True

def signal_handler(signum, frame):
    """处理Ctrl+C信号"""
    global running
    print(Fore.YELLOW + "\n\n[  中断  ] 检测到Ctrl+C，正在安全退出...")
    running = False

def setup_signal_handlers():
    """设置信号处理器"""
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)

def main():
    global running
    
    init()  # 初始化colorama
    setup_signal_handlers()  # 设置信号处理器
    
    print(Fore.CYAN + "=" * 50)
    print(Fore.CYAN + "      Ctrl+C 测试程序")
    print(Fore.CYAN + "=" * 50)
    print(Fore.YELLOW + "提示: 按 Ctrl+C 可以安全退出程序")
    print(Fore.WHITE + "程序将运行30秒，或直到你按Ctrl+C")
    print(Fore.CYAN + "=" * 50)
    
    count = 0
    max_count = 30
    
    try:
        while running and count < max_count:
            count += 1
            print(Fore.GREEN + f"[  {count:02d}  ] 程序运行中... (剩余 {max_count - count} 秒)")
            
            # 可中断的延迟
            delay_start = time.time()
            while time.time() - delay_start < 1.0 and running:
                time.sleep(0.1)
            
            if not running:
                print(Fore.YELLOW + f"[  中断  ] 在第 {count} 秒时退出")
                break
                
    except KeyboardInterrupt:
        print(Fore.YELLOW + "\n[  中断  ] 程序被用户中断")
        running = False
        
    finally:
        print(Fore.CYAN + "\n" + "=" * 50)
        if running:
            print(Fore.CYAN + "         程序正常完成")
        else:
            print(Fore.CYAN + "         程序被中断")
        print(Fore.CYAN + "=" * 50)
        print(Fore.WHITE + f"运行时间: {count} 秒")
        if not running:
            print(Fore.YELLOW + f"状态: 用户中断退出")
        else:
            print(Fore.GREEN + f"状态: 正常完成")
        print(Fore.CYAN + "=" * 50)
        print(Fore.WHITE + "测试完成！")

if __name__ == "__main__":
    main()
