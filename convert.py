import cmd2
import socket
import threading
import time

class SocketApp(cmd2.Cmd):
    """Simple command processor for socket operations."""
    
    def __init__(self):
        super().__init__()
        self.sock = None
        self.keep_sending = False
        self.sending_thread = None

    def do_connect(self, args):
        """Connect to a server: connect <ip> <port>"""
        try:
            ip, port = args.split()
            port = int(port)
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((ip, port))
            self.keep_sending = True
            self.start_sending_thread()
            self.poutput(f"Connected to {ip} on port {port}")
        except ValueError:
            self.poutput("Invalid arguments. Usage: connect <ip> <port>")
        except socket.error as e:
            self.poutput(f"Socket error: {e}")

    def do_send(self, args):
        """Send data to the server: send <data>"""
        if self.sock is None:
            self.poutput("Not connected to any server. Use connect command first.")
            return
        try:
            self.sock.sendall(args.encode())
            self.poutput(f"Sent: {args}")
        except socket.error as e:
            self.poutput(f"Socket error: {e}")

    def do_close(self, args):
        """Close the socket connection"""
        if self.sock is not None:
            self.keep_sending = False
            if self.sending_thread is not None:
                self.sending_thread.join()
            self.sock.close()
            self.sock = None
            self.poutput("Connection closed")
        else:
            self.poutput("No active connection to close.")

    def start_sending_thread(self):
        """Start the background thread to send data periodically."""
        if self.sending_thread is None or not self.sending_thread.is_alive():
            self.sending_thread = threading.Thread(target=self.send_periodically)
            self.sending_thread.daemon = True
            self.sending_thread.start()

    def send_periodically(self):
        """Send data periodically to the server every 1.8 seconds."""
        while self.keep_sending:
            try:
                if self.sock:
                    self.sock.sendall(b'3e80')
                    # self.poutput("Sent: 3e80")
                time.sleep(1.8)
            except socket.error as e:
                self.poutput(f"Socket error during periodic send: {e}")
                break

    def postcmd(self, stop, line):
        """Hook method executed just after a command dispatch is finished."""
        if self.keep_sending:
            self.start_sending_thread()
        return stop

if __name__ == '__main__':
    app = SocketApp()
    app.cmdloop()
