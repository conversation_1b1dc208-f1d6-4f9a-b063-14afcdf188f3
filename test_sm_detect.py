#!/usr/bin/env python3
"""
测试国密算法检测系统
"""

import sys
import os
import tempfile
import zipfile
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # 由于文件名包含空格，需要特殊处理导入
    import importlib.util
    spec = importlib.util.spec_from_file_location("sm_detect", "SM detect.py")
    sm_detect = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(sm_detect)

    APKAnalyzer = sm_detect.APKAnalyzer
    SMCryptoConstants = sm_detect.SMCryptoConstants
    print_report = sm_detect.print_report

except Exception as e:
    print(f"导入模块失败: {e}")
    print("请确保 'SM detect.py' 文件存在且可导入")
    sys.exit(1)


def create_test_apk():
    """创建一个包含测试数据的模拟APK文件"""
    # 创建临时APK文件
    temp_apk = tempfile.NamedTemporaryFile(suffix='.apk', delete=False)
    
    with zipfile.ZipFile(temp_apk.name, 'w') as apk_zip:
        # 添加一个模拟的DEX文件
        dex_content = b'dex\n035\x00' + b'\x00' * 100  # 简单的DEX文件头
        apk_zip.writestr('classes.dex', dex_content)
        
        # 添加一个模拟的SO文件（包含一些测试数据）
        so_content = b'\x7fELF' + b'\x00' * 100  # 简单的ELF文件头
        # 添加一些SM4的SBox数据作为测试
        constants = SMCryptoConstants()
        sm4_sbox_hex = ''.join([hex(b)[2:].upper().zfill(2) for b in constants.SM4_SBOX])
        so_content += bytes.fromhex(sm4_sbox_hex)
        apk_zip.writestr('lib/arm64-v8a/libtest.so', so_content)
        
        # 添加AndroidManifest.xml
        manifest_content = '''<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.test.smcrypto">
    <application android:label="Test App">
        <activity android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>'''
        apk_zip.writestr('AndroidManifest.xml', manifest_content.encode('utf-8'))
    
    return temp_apk.name


def test_constants():
    """测试国密算法常量"""
    print("测试国密算法常量...")
    constants = SMCryptoConstants()
    
    # 测试SM2常量
    assert len(constants.SM2_CURVE_P) == 64, "SM2 curve P 长度错误"
    assert len(constants.SM2_CURVE_A) == 64, "SM2 curve A 长度错误"
    
    # 测试SM3常量
    assert len(constants.SM3_T_ARRAY) == 2, "SM3 T数组长度错误"
    assert len(constants.SM3_IV) == 8, "SM3 IV数组长度错误"
    
    # 测试SM4常量
    assert len(constants.SM4_SBOX) == 256, "SM4 SBox长度错误"
    assert len(constants.SM4_CK) == 32, "SM4 CK数组长度错误"
    assert len(constants.SM4_FK) == 4, "SM4 FK数组长度错误"
    
    # 测试获取所有常量
    all_constants = constants.get_all_constants()
    assert 'SM2' in all_constants, "缺少SM2常量"
    assert 'SM3' in all_constants, "缺少SM3常量"
    assert 'SM4' in all_constants, "缺少SM4常量"
    
    print("✓ 国密算法常量测试通过")


def test_apk_analyzer():
    """测试APK分析器"""
    print("测试APK分析器...")
    
    # 创建测试APK
    test_apk_path = create_test_apk()
    
    try:
        # 创建分析器
        analyzer = APKAnalyzer(test_apk_path)
        
        # 测试APK提取
        java_files, native_files = analyzer.extract_apk()
        assert len(java_files) > 0, "未找到DEX文件"
        assert len(native_files) > 0, "未找到SO文件"
        print(f"✓ 发现 {len(java_files)} 个DEX文件和 {len(native_files)} 个SO文件")
        
        # 测试关键字检查
        assert analyzer._check_crypto_keywords("SM2Cipher"), "SM2关键字检查失败"
        assert analyzer._check_crypto_keywords("sm3hash"), "SM3关键字检查失败"
        assert analyzer._check_crypto_keywords("SMS4Encrypt"), "SMS4关键字检查失败"
        assert not analyzer._check_crypto_keywords("normal_function"), "关键字检查误报"
        print("✓ 关键字检查测试通过")
        
        # 测试算法识别
        assert analyzer._identify_algorithm("SM2Cipher") == "SM2", "SM2算法识别失败"
        assert analyzer._identify_algorithm("sm3hash") == "SM3", "SM3算法识别失败"
        assert analyzer._identify_algorithm("SMS4Encrypt") == "SM4", "SMS4算法识别失败"
        print("✓ 算法识别测试通过")
        
        # 测试干扰项过滤
        test_candidates = [
            {'type': 'test', 'value': 'SM2Cipher', 'algorithm': 'SM2'},
            {'type': 'test', 'value': 'isM3U8File', 'algorithm': 'SM3'},  # 应被过滤
            {'type': 'test', 'class': 'lambda$test$1', 'algorithm': 'SM4'},  # 应被过滤
        ]
        filtered = analyzer.filter_interference(test_candidates)
        assert len(filtered) == 1, f"过滤结果错误，期望1个，实际{len(filtered)}个"
        assert filtered[0]['value'] == 'SM2Cipher', "过滤结果错误"
        print("✓ 干扰项过滤测试通过")
        
    finally:
        # 清理测试文件
        os.unlink(test_apk_path)
    
    print("✓ APK分析器测试通过")


def test_full_analysis():
    """测试完整分析流程"""
    print("测试完整分析流程...")
    
    # 创建测试APK
    test_apk_path = create_test_apk()
    
    try:
        # 创建分析器并执行分析
        analyzer = APKAnalyzer(test_apk_path)
        report = analyzer.analyze()
        
        # 检查报告结构
        assert 'apk_file' in report, "报告缺少APK文件信息"
        assert 'analysis_summary' in report, "报告缺少分析摘要"
        assert 'algorithms_found' in report, "报告缺少算法发现信息"
        assert 'detailed_results' in report, "报告缺少详细结果"
        
        print("✓ 报告结构正确")
        
        # 打印报告（测试报告生成功能）
        print("\n--- 测试报告输出 ---")
        print_report(report)
        print("--- 测试报告输出结束 ---\n")
        
        print("✓ 完整分析流程测试通过")
        
    finally:
        # 清理测试文件
        os.unlink(test_apk_path)


def main():
    """运行所有测试"""
    print("开始运行国密算法检测系统测试...")
    print("="*60)
    
    try:
        test_constants()
        test_apk_analyzer()
        test_full_analysis()
        
        print("="*60)
        print("✓ 所有测试通过！")
        
    except AssertionError as e:
        print(f"✗ 测试失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
