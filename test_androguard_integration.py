#!/usr/bin/env python3
"""
测试androguard集成
"""

import sys
import os

# 导入我们的模块
import importlib.util
spec = importlib.util.spec_from_file_location("sm_detect", "SM detect.py")
sm_detect = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sm_detect)

APKAnalyzer = sm_detect.APKAnalyzer


def test_androguard_availability():
    """测试androguard可用性"""
    print("测试androguard可用性...")
    
    # 检查全局变量
    print(f"ANDROGUARD_AVAILABLE: {sm_detect.ANDROGUARD_AVAILABLE}")
    print(f"ANDROGUARD_VERSION: {sm_detect.ANDROGUARD_VERSION}")
    
    if sm_detect.ANDROGUARD_AVAILABLE:
        print("✓ androguard可用")
        
        # 测试导入
        if sm_detect.ANDROGUARD_VERSION == "new":
            try:
                from androguard.core.apk import APK
                print("✓ 新版本androguard导入成功")
            except ImportError as e:
                print(f"✗ 新版本androguard导入失败: {e}")
        else:
            try:
                from androguard.core.bytecodes import apk
                print("✓ 旧版本androguard导入成功")
            except ImportError as e:
                print(f"✗ 旧版本androguard导入失败: {e}")
    else:
        print("✗ androguard不可用")


def test_real_apk_analysis():
    """测试真实APK分析（如果有的话）"""
    print("\n测试真实APK分析...")
    
    # 检查是否有真实的APK文件可以测试
    test_apks = [
        "demo_sm_crypto.apk",
        # 可以添加其他测试APK
    ]
    
    for apk_file in test_apks:
        if os.path.exists(apk_file):
            print(f"测试APK文件: {apk_file}")
            
            try:
                analyzer = APKAnalyzer(apk_file)
                
                # 只测试Java代码分析部分
                java_files, _ = analyzer.extract_apk()
                print(f"发现 {len(java_files)} 个DEX文件")
                
                if sm_detect.ANDROGUARD_AVAILABLE:
                    java_candidates = analyzer.analyze_java_code(java_files)
                    print(f"Java代码分析结果: {len(java_candidates)} 个候选项")
                    
                    for candidate in java_candidates:
                        print(f"  - {candidate['type']}: {candidate.get('name', candidate.get('method', 'unknown'))}")
                else:
                    print("跳过Java代码分析（androguard不可用）")
                    
            except Exception as e:
                print(f"分析APK时出错: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"APK文件不存在: {apk_file}")


def test_java_keyword_detection():
    """测试Java关键字检测"""
    print("\n测试Java关键字检测...")
    
    analyzer = APKAnalyzer("dummy.apk")
    
    test_cases = [
        ("SM2Cipher", True),
        ("SM3Hash", True),
        ("SM4Encrypt", True),
        ("SMS4Decrypt", True),
        ("NormalClass", False),
        ("isM3U8File", True),  # 这个应该被检测到，但会被过滤
    ]
    
    for test_string, expected in test_cases:
        result = analyzer._check_crypto_keywords(test_string)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{test_string}' -> {result} (期望: {expected})")


def main():
    """运行所有测试"""
    print("开始测试androguard集成...")
    print("="*60)
    
    try:
        test_androguard_availability()
        test_java_keyword_detection()
        test_real_apk_analysis()
        
        print("="*60)
        print("✓ androguard集成测试完成")
        
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
