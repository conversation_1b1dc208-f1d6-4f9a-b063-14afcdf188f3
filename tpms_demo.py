#!/usr/bin/env python3
"""
TPMS演示脚本
展示完整的TPMS信号分析、修改和暴力破解流程
"""

import subprocess
import time
import os

def run_command(cmd, description, wait_for_input=False):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"演示: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    if wait_for_input:
        input("按回车键继续...")
    
    try:
        if wait_for_input and "brute-force" in cmd:
            # 对于暴力破解，需要自动输入确认
            process = subprocess.Popen(cmd, shell=True, stdin=subprocess.PIPE, 
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            stdout, stderr = process.communicate(input="y\n")
            print("STDOUT:")
            print(stdout)
            if stderr:
                print("STDERR:")
                print(stderr)
        else:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            print("STDOUT:")
            print(result.stdout)
            if result.stderr:
                print("STDERR:")
                print(result.stderr)
        
        print("✓ 命令执行完成")
        return True
        
    except subprocess.TimeoutExpired:
        print("✗ 命令执行超时")
        return False
    except Exception as e:
        print(f"✗ 执行错误: {e}")
        return False

def main():
    print("🚗 TPMS信号生成器演示")
    print("="*60)
    print("本演示将展示TPMS信号的分析、修改和暴力破解功能")
    print("注意：这是一个教育和研究工具，请遵守当地法律法规")
    
    demos = [
        {
            "cmd": "python tpms.py --mode analyze",
            "desc": "1. 分析原始TPMS数据的CRC校验和",
            "wait": True
        },
        {
            "cmd": "python tpms.py --mode modify --pressure 50 --temperature 25",
            "desc": "2. 修改压力和温度数据",
            "wait": True
        },
        {
            "cmd": "python tpms.py --mode simulate --pressure 100 --temperature 30",
            "desc": "3. 生成修改后数据的信号文件",
            "wait": True
        },
        {
            "cmd": "python tpms.py --mode brute-force --start-crc 00 --end-crc 03 --crc-interval 0.1",
            "desc": "4. CRC暴力破解演示 (小范围)",
            "wait": True
        },
        {
            "cmd": "python tpms_batch_manager.py --action list",
            "desc": "5. 列出生成的CRC文件",
            "wait": True
        },
        {
            "cmd": "python tpms_batch_manager.py --action analyze",
            "desc": "6. 分析CRC覆盖范围",
            "wait": True
        }
    ]
    
    for i, demo in enumerate(demos, 1):
        success = run_command(demo["cmd"], demo["desc"], demo["wait"])
        
        if not success:
            print(f"演示 {i} 失败，继续下一个...")
        
        if i < len(demos):
            time.sleep(1)
    
    print(f"\n{'='*60}")
    print("🎉 演示完成!")
    print("\n生成的文件:")
    
    # 列出生成的文件
    files = [
        "tpms_signal.bin",
        "tpms_crc_00.bin",
        "tpms_crc_01.bin", 
        "tpms_crc_02.bin",
        "tpms_crc_03.bin"
    ]
    
    for filename in files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  ✓ {filename} ({size} bytes)")
        else:
            print(f"  ✗ {filename} (未找到)")
    
    print(f"\n{'='*60}")
    print("📖 使用说明:")
    print("1. 使用GNU Radio Companion加载生成的.bin文件")
    print("2. 配置HackRF发送器参数:")
    print("   - 采样率: 2 MHz")
    print("   - 载波频率: 433.92 MHz")
    print("   - 增益: 47 dB")
    print("3. 运行流图开始发送信号")
    print("\n⚠️  重要提醒:")
    print("- 仅用于研究和教育目的")
    print("- 遵守当地无线电法规")
    print("- 不要干扰正常的TPMS系统")
    print("- 在屏蔽环境中进行测试")

if __name__ == "__main__":
    main()
