import os
import sys
import signal
import binascii
from datetime import datetime
from scapy.all import *

# Function to convert bytes to hex format for easier reading
def hexdump(x):
    return ' '.join(f'{c:02x}' for c in x)

# Signal handler for clean exit on Ctrl+C
def signalhandler(signal, id):
    print("!Killing")
    sys.exit(0)

# Function to send ICMP, ARP, or TCP packets and capture responses
def spawn(host, packet_type, count=10, tcp_port=445):
    padding_data = []

    # Create filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"etherleak_data_{host}_{packet_type}_{timestamp}.txt"

    # Open file for writing
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"Etherleak Data Capture\n")
        f.write(f"Target: {host}\n")
        f.write(f"Packet Type: {packet_type.upper()}\n")
        if packet_type == 'tcp':
            f.write(f"TCP Port: {tcp_port}\n")
        f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Packets: {count}\n")
        f.write("-" * 50 + "\n\n")

        for i in range(count):
            if packet_type == 'arp':
                packet = ARP(pdst=host)
            elif packet_type == 'icmp':
                packet = IP(dst=host) / ICMP(type=8) / 'x'  # ICMP echo request
            elif packet_type == 'tcp':
                packet = IP(dst=host) / TCP(dport=tcp_port)  # TCP to specified port

            # Send packet and capture response
            resp = sr1(packet, timeout=2, verbose=0)
            if resp and Padding in resp:
                data = resp[Padding].load
                padding_data.append(data)
                hex_data = hexdump(data)
                print(f"Padding captured: {hex_data}")
                # Save to file - each padding data on a new line
                f.write(f"{hex_data}\n")
            else:
                print(f"No Padding layer found or no response received on attempt {i+1}/{count}.")
                f.write(f"No padding data - attempt {i+1}\n")

    print(f"\nData saved to: {filename}")
    return padding_data

# Function to analyze captured padding data
def analyze_padding(padding_data, host, packet_type):
    if len(padding_data) < 2:
        print("\nInsufficient data captured for Etherleak analysis. Try increasing the number of packets captured.")
        return False

    # Check for variation in padding data
    is_varying = not all(data == padding_data[0] for data in padding_data)

    # Create analysis report filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    analysis_filename = f"etherleak_analysis_{host}_{packet_type}_{timestamp}.txt"

    # Save analysis results
    with open(analysis_filename, 'w', encoding='utf-8') as f:
        f.write(f"Etherleak Vulnerability Analysis Report\n")
        f.write(f"=" * 50 + "\n")
        f.write(f"Target: {host}\n")
        f.write(f"Packet Type: {packet_type.upper()}\n")
        f.write(f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Padding Data Captured: {len(padding_data)}\n\n")

        if is_varying:
            f.write("RESULT: POTENTIAL ETHERLEAK VULNERABILITY DETECTED\n")
            f.write("Variation in padding data detected across packets.\n\n")
            print("\nVariation in padding data detected across packets, indicating a potential Etherleak vulnerability.")
        else:
            f.write("RESULT: NO ETHERLEAK VULNERABILITY DETECTED\n")
            f.write("No significant variation in padding data observed.\n\n")
            print("\nNo significant variation in padding data observed. Target may not exhibit Etherleak vulnerability.")

        f.write("Captured Padding Data:\n")
        f.write("-" * 30 + "\n")
        for i, data in enumerate(padding_data, 1):
            hex_data = hexdump(data)
            f.write(f"{i:2d}: {hex_data}\n")

        # Statistical analysis
        unique_data = list(set(hexdump(data) for data in padding_data))
        f.write(f"\nStatistical Summary:\n")
        f.write(f"Total packets with padding: {len(padding_data)}\n")
        f.write(f"Unique padding patterns: {len(unique_data)}\n")
        f.write(f"Variation rate: {len(unique_data)/len(padding_data)*100:.1f}%\n")

    print(f"Analysis report saved to: {analysis_filename}")

    # Return whether padding data varied
    return is_varying

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signalhandler)

    if len(sys.argv) < 4 or len(sys.argv) > 5:
        print("Usage: sudo python etherleak_checker.py <target_ip> <arp|icmp|tcp> <count> [tcp_port]")
        sys.exit(1)

    target_host = sys.argv[1]
    packet_type = sys.argv[2]
    packet_count = int(sys.argv[3])
    
    # Set default TCP port to 445 if not provided
    if len(sys.argv) == 5:
        tcp_port = int(sys.argv[4])
    else:
        tcp_port = 445

    if packet_type not in ['arp', 'icmp', 'tcp']:
        print("Invalid type! Use 'arp', 'icmp', or 'tcp'.")
        sys.exit(0)

    print(f"[ Targeting {target_host} using {packet_type.upper()} for {packet_count} requests... ]")

    if packet_type == 'tcp':
        print(f"[ Using TCP port {tcp_port} ]")

    # Capture and analyze the padding data
    captured_data = spawn(target_host, packet_type, packet_count, tcp_port)

    if captured_data:
        analyze_padding(captured_data, target_host, packet_type)

    print("\nPadding analysis complete.")
