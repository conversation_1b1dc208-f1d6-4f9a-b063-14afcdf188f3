整车：
172.20.1.1 root 7OrxIgu^PpRH
读版本：
cat /oemapp/fy/bin/common/conf/build.prop
root@s32g399ardb3:~# cd /oemapp/fy/bin
root@s32g399ardb3:/oemapp/fy/bin# cat /oemapp/fy/bin/common/conf/build.prop
#保存系统默认，不可配置，不可变动
#版本号
ro.verno.linux=VDF_P0336304_BL012_RC_AB.HOMO_V2_EU_S32G_A
ro.verno.linux_internal=FY25.PF.VDFS_NXP_A53.LX.22.00.01.HM_V2.001.01
ro.verno.linux-bsp=BGW.NXP_S32G399A.BSP35.VIRTIO_HOMO_1.01.003
ro.builder_machine=root@f54efdf6dc57
ro.builder_time=Fri_Apr_19_10:24:48_UTC_2024
root@s32g399ardb3:/oemapp/fy/bin# ssh root@***********


~ # cat /oemapp/fy/bin/common/conf/build.prop
#保存系统默认，不可配置，不可变动
#版本号
ro.verno.linux=VDF_P0336304_BL012_RC_AA.HOMO_V1_AG35_EU_SA
ro.verno.linux_internal=FY25.EU.SAFS_QUE_AG35.LX.22.00.01.HOMO_V1.001.01
ro.verno.linux-bsp=SA.QUECTEL_AG35.EU.BSP.2.06.006.IMAGE
ro.builder_machine=jenkins@7e6e13dfd201
ro.builder_time=Thu_Mar_21_09:36:07_UTC_2024

VDF/SA: 默认密码：FyVdf@123
 SSH密码（密码生成：hephaestus.nioint.com）：7OrxIgu^PpRH
 ssh root@**********
 ssh root@***********

CDC： 固定密码：nex2genEVMaker!#22
 ssh root@**********1
   强制打开：8155串口 /usr/sbin/sshd

ADC： J5： ssh -oHostKeyAlgorithms=+ssh-rsa nio@*********** 
 passswd：fy123，H7aCy_V53@28WL9Zq*0U
 Vxworks：ssh -oHostKeyAlgorithms=+ssh-rsa nio@*********** 
 passswd：nio，F9W@q90vrronwZha0_DB
 scp -oHostKeyAlgorithms=+ssh-rsa /path/to/local/file username@remotehost:/path/to/remote/directory