def little_endian_to_big_endian(hex_string):
    little_endian_bytes = bytes.fromhex(hex_string)
    big_endian_bytes = little_endian_bytes[::-1]
    big_endian_hex = big_endian_bytes.hex()
    print(f"原始十六进制数: {hex_string}")
    print(f"大端格式的字节串: {big_endian_bytes.hex()}")
    print(f"大端格式的整数: {big_endian_hex}")

def to_little(hex_value):

    # 将整数转换为小端格式的字节串
    little_endian_bytes = hex_value.to_bytes(2, byteorder='little')

    # 将小端字节串转换为整数
    little_endian_int = int.from_bytes(little_endian_bytes, byteorder='little')

    print(f"原始十六进制数: {hex_value}")
    print(f"小端格式的字节串: {little_endian_bytes.hex()}")
    print(f"小端格式的整数: {little_endian_int}")

# to_little()
little_endian_to_big_endian('D746B67A0A7C56E72B35E9A65ABF943B42779EF0A12182F0183D8745B2E771C8')