"""
Base class for random number generators used in dieharder testing.
"""

import time
from abc import ABC, abstractmethod
from typing import List, Union
import numpy as np


class BaseGenerator(ABC):
    """Abstract base class for random number generators"""
    
    def __init__(self, seed: int):
        """Initialize the generator with a seed"""
        self.seed = seed
        self._performance_samples = 0
        self._performance_time = 0.0
        self._setup_generator(seed)
    
    @abstractmethod
    def _setup_generator(self, seed: int):
        """Setup the specific generator implementation"""
        pass
    
    @abstractmethod
    def random_uint32(self) -> int:
        """Generate a random 32-bit unsigned integer"""
        pass
    
    def random_float(self) -> float:
        """Generate a random float in [0, 1)"""
        return self.random_uint32() / (2**32)
    
    def random_bytes(self, n: int) -> bytes:
        """Generate n random bytes"""
        result = bytearray()
        for _ in range(n // 4):
            val = self.random_uint32()
            result.extend(val.to_bytes(4, 'little'))
        
        # Handle remaining bytes
        remaining = n % 4
        if remaining:
            val = self.random_uint32()
            result.extend(val.to_bytes(4, 'little')[:remaining])
        
        return bytes(result)
    
    def random_bits(self, n: int) -> List[int]:
        """Generate n random bits (0 or 1)"""
        bits = []
        bytes_needed = (n + 7) // 8
        random_bytes = self.random_bytes(bytes_needed)
        
        for byte in random_bytes:
            for i in range(8):
                if len(bits) >= n:
                    break
                bits.append((byte >> i) & 1)
        
        return bits[:n]
    
    def random_array(self, size: int, dtype: str = 'uint32') -> np.ndarray:
        """Generate an array of random numbers"""
        if dtype == 'uint32':
            return np.array([self.random_uint32() for _ in range(size)], dtype=np.uint32)
        elif dtype == 'float':
            return np.array([self.random_float() for _ in range(size)], dtype=np.float64)
        elif dtype == 'bits':
            return np.array(self.random_bits(size), dtype=np.uint8)
        else:
            raise ValueError(f"Unsupported dtype: {dtype}")
    
    def get_performance(self) -> float:
        """Measure and return the performance in randoms per second"""
        if self._performance_time == 0:
            # Perform a quick benchmark
            start_time = time.time()
            sample_count = 100000
            
            for _ in range(sample_count):
                self.random_uint32()
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            if elapsed > 0:
                self._performance_time = elapsed
                self._performance_samples = sample_count
                return sample_count / elapsed
            else:
                return 1e7  # Default fallback
        else:
            return self._performance_samples / self._performance_time
    
    def reset(self, seed: int = None):
        """Reset the generator with a new seed"""
        if seed is None:
            seed = self.seed
        else:
            self.seed = seed
        self._setup_generator(seed)
    
    def __str__(self):
        return f"{self.__class__.__name__}(seed={self.seed})"
    
    def __repr__(self):
        return self.__str__()
