#!/usr/bin/env python3
"""
TPMS项目依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def main():
    print("=== TPMS项目依赖安装 ===")
    
    # 必需的包
    required_packages = [
        "numpy",
        "argparse"
    ]
    
    # 可选的包 (HackRF相关)
    optional_packages = [
        "hackrf"  # 注意：这个包可能需要特殊安装
    ]
    
    print("安装必需的依赖包...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n必需包安装完成: {success_count}/{len(required_packages)}")
    
    print("\n尝试安装可选依赖包...")
    print("注意: HackRF包可能需要额外的系统依赖")
    
    for package in optional_packages:
        install_package(package)
    
    print("\n=== 安装完成 ===")
    print("如果HackRF包安装失败，可以:")
    print("1. 在Windows上: 下载预编译的wheel文件")
    print("2. 在Linux上: sudo apt-get install hackrf libhackrf-dev")
    print("3. 使用模拟模式运行程序")
    
    print("\n运行程序:")
    print("python tpms.py                    # 交互模式")
    print("python tpms.py --mode analyze     # 分析模式")
    print("python tpms.py --mode simulate    # 模拟模式")

if __name__ == "__main__":
    main()
