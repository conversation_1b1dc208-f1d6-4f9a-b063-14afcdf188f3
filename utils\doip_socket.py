from scapy.supersocket import StreamSocket
from scapy.contrib.automotive import log_automotive
from scapy.contrib.automotive.doip import DoIP
import socket
from scapy.sendrecv import sr1
from scapy.packet import Packet
# from scapy.all import *
from scapy.main import load_contrib
load_contrib("automotive.doip")



class my_DoIPSocket(StreamSocket):
    def __init__(self, ip='127.0.0.1', port=13400, activate_routing=True,
                 source_address=0xe80, target_address=0,
                 activation_type=0, reserved_oem=b"", clientType=1):
        # type: (str, int, bool, int, int, int, bytes, bool, int) -> None
        self.ip = ip
        self.port = port
        self.source_address = source_address
        self.client = clientType
        try:
            self._init_socket()

            if activate_routing:
                self._activate_routing(
                    source_address, target_address, activation_type, reserved_oem)
        except socket.timeout:
            print('Socket Connect Faild！！！')
            
    def _init_socket(self, sock_family=socket.AF_INET):
        s = socket.socket(sock_family, socket.SOCK_STREAM)
        s.settimeout(5)
        s.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        if self.client:
            addrinfo = socket.getaddrinfo(self.ip, self.port, proto=socket.IPPROTO_TCP)
            s.connect(addrinfo[0][-1])
            if s:
                print('Socket Connect Success！！！')
                StreamSocket.__init__(self, s, DoIP)
            else:
                print('Socket Connect Faild！！！')

        else:
            addrinfo = socket.getaddrinfo("0.0.0.0", self.port, proto=socket.IPPROTO_TCP)
            s.bind(addrinfo[0][-1])
            s.listen(5)
            while True:
                conn,addr = s.accept()
                if addr[0] == self.ip:
                    break
                else:
                    conn.close()
            if conn:
                print('Socket Connect Success！！！')
                StreamSocket.__init__(self, conn, DoIP)
            else:
                print('Socket Connect Faild！！！')


    def _activate_routing(self,
                          source_address,  # type: int
                          target_address,  # type: int
                          activation_type,  # type: int
                          reserved_oem=b""  # type: bytes
                          ):  # type: (...) -> None
        resp = self.sr1(
            DoIP(payload_type=0x5, activation_type=activation_type,
                 source_address=source_address, reserved_oem=reserved_oem),
            verbose=False, timeout=1)
        if resp and resp.payload_type == 0x6 and \
                resp.routing_activation_response == 0x10:
            self.target_address = target_address or \
                resp.logical_address_doip_entity
            log_automotive.info(
                "Routing activation successful! Target address set to: 0x%x",
                self.target_address)
        else:
            log_automotive.error("Routing activation failed!")
            print('May not be necessary to Routing activation')
            print('Please manually set the target address')