# TPMS信号生成器项目总结

## 项目概述

本项目实现了一个完整的胎压监测系统(TPMS)信号生成、分析和发送工具，支持使用HackRF设备进行射频信号传输。项目的核心功能是**CRC暴力破解**，能够从0x00到0xFF逐一尝试所有可能的校验和值并发送对应的信号。

## 核心功能

### 1. 信号分析
- 解析TPMS数据包结构
- 支持多种CRC算法验证
- 自动识别数据字段含义

### 2. 数据修改
- 修改压力值 (0-255)
- 修改温度值 (0-255) 
- 修改传感器ID (8位十六进制)
- 自动重新计算校验和

### 3. **CRC暴力破解** ⭐
- **从0x00到0xFF完整暴力破解**
- **可配置CRC范围和发送间隔**
- **支持HackRF实时发送或文件保存**
- **批量生成256个不同CRC的信号文件**

### 4. 信号生成
- 曼彻斯特编码
- 调频(FM)调制
- 433.92 MHz载波频率
- 2 MHz采样率

### 5. HackRF发送
- 实时射频信号发送
- 可配置发送参数
- 支持重复发送

## 文件结构

```
├── tpms.py                    # 主程序
├── tpms_batch_manager.py      # 批量文件管理器
├── tpms_demo.py              # 演示脚本
├── test_tpms.py              # 测试脚本
├── install_tpms_dependencies.py # 依赖安装
├── TPMS_README.md            # 使用说明
└── TPMS_PROJECT_SUMMARY.md   # 项目总结
```

## 关键技术实现

### TPMS数据包格式
```
原始数据: 00000000014d0414de0104a6012a06
├── 唤醒音: 0000000001 (5字节)
├── 头字节: 4d (1字节)
├── 功能码: 04 (1字节)  
├── 加速度码: 14 (1字节)
├── 传感器ID: de0104a6 (4字节)
├── 压力: 01 (1字节)
├── 温度: 2a (1字节)
└── 校验和: 06 (1字节) ← **暴力破解目标**
```

### CRC暴力破解算法
```python
def brute_force_crc_transmit(self, data_without_crc, start_crc=0x00, end_crc=0xFF):
    for crc_value in range(start_crc, end_crc + 1):
        full_packet = data_without_crc + f"{crc_value:02x}"
        signal = self.generate_signal(full_packet)
        # 发送信号或保存到文件
        transmit_signal(signal)
```

## 使用场景

### 1. 研究用途
- TPMS协议分析
- 射频信号研究
- 安全漏洞测试

### 2. 暴力破解应用
- **测试TPMS接收器的CRC验证机制**
- **寻找有效的校验和算法**
- **验证不同CRC值的系统响应**

### 3. 教育目的
- 无线通信原理教学
- 数字信号处理实验
- 网络安全课程演示

## 命令行使用示例

### 基本功能
```bash
# 分析原始数据
python tpms.py --mode analyze

# 修改数据
python tpms.py --mode modify --pressure 50 --temperature 25

# 模拟发送
python tpms.py --mode simulate
```

### **CRC暴力破解** 🔥
```bash
# 完整暴力破解 (0x00-0xFF)
python tpms.py --mode brute-force

# 指定范围暴力破解
python tpms.py --mode brute-force --start-crc 00 --end-crc 0f --crc-interval 0.5

# 暴力破解修改后的数据
python tpms.py --mode brute-force --pressure 100 --temperature 30
```

### 批量管理
```bash
# 列出所有CRC文件
python tpms_batch_manager.py --action list

# 批量发送 (需要HackRF)
python tpms_batch_manager.py --action transmit

# 清理文件
python tpms_batch_manager.py --action cleanup
```

## 技术特点

### 1. 完整的CRC暴力破解
- ✅ 支持0x00-0xFF全范围暴力破解
- ✅ 可配置起始和结束CRC值
- ✅ 可调节发送间隔
- ✅ 支持实时发送和文件保存

### 2. 多种CRC算法支持
- CRC8 (多种多项式)
- 简单校验和
- XOR校验和
- 二进制补码校验和

### 3. 灵活的信号生成
- 曼彻斯特编码
- FM调制
- 可配置载波频率
- 标准TPMS参数

### 4. HackRF集成
- 直接硬件控制
- 实时信号发送
- 可配置发送参数

## 安全和法律考虑

⚠️ **重要提醒**:
- 本工具仅用于研究和教育目的
- 请遵守当地无线电法规
- 不要干扰正常的TPMS系统
- 建议在屏蔽环境中进行测试
- 使用前请获得适当的授权

## 项目亮点

1. **完整的CRC暴力破解实现** - 这是项目的核心功能
2. **实用的批量管理工具** - 方便处理大量生成的文件
3. **详细的技术文档** - 便于理解和扩展
4. **模块化设计** - 易于维护和修改
5. **教育价值** - 适合学习无线通信和信号处理

## 扩展可能

- 支持更多TPMS协议
- 添加信号解码功能
- 实现自动化测试框架
- 集成更多SDR设备
- 添加图形用户界面

---

**项目状态**: ✅ 完成
**核心功能**: ✅ CRC暴力破解 (0x00-0xFF)
**测试状态**: ✅ 已验证
**文档状态**: ✅ 完整
