from openpyxl import load_workbook
from docxtpl import DocxTemplate

FROM_EXCEL = 'SOA架构安全加固性能测试用例.xlsx'
SHEET_NAME = ''
TEMPLATE_DOCX = "SOA tamp.docx"
OUTPUT = "SOA架构安全加固性能测试报告.docx"

# Open the Excel file
wb = load_workbook(FROM_EXCEL)

# List all available sheets and let user select multiple sheets
sheet_names = wb.sheetnames
print("Available sheets:")
for i, name in enumerate(sheet_names, 1):
    print(f"{i}. {name}")

# Get user selection for multiple sheets
while True:
    try:
        selection_input = input("Enter the numbers of the sheets you want to use (comma-separated, e.g., 2,3,4,5,6): ")
        selections = [int(s.strip()) for s in selection_input.split(',')]

        # Validate all selections
        valid_selections = True
        for selection in selections:
            if not (1 <= selection <= len(sheet_names)):
                print(f"Invalid selection: {selection}. Please enter numbers between 1 and {len(sheet_names)}")
                valid_selections = False
                break

        if valid_selections:
            selected_sheet_names = [sheet_names[selection - 1] for selection in selections]
            print(f"Selected sheets: {', '.join(selected_sheet_names)}")
            break
    except ValueError:
        print("Please enter valid numbers separated by commas")

# Initialize an empty list to store all rows from all selected sheets
rows = []

# Process each selected sheet
for sheet_name in selected_sheet_names:
    print(f"Processing sheet: {sheet_name}")
    sheet = wb[sheet_name]

    # Get headers from the first row of the sheet
    headers = [cell.value for cell in sheet[1]]

    # Extract data from the current sheet and append to rows
    sheet_rows = [
        {headers[j]: sheet[i][j].value for j in range(len(headers))}
        for i in range(2, sheet.max_row + 1)
    ]

    # Add sheet name to each row for reference
    for row in sheet_rows:
        row['Sheet_Name'] = sheet_name

    # Append the rows from this sheet to the combined rows list
    rows.extend(sheet_rows)


rows2 = []

for i in range(len(rows)):
    # Skip rows with no case number
    if rows[i].get('用例编号') is None:
        continue

    # Process each row
    for k, v in rows[i].items():
        rows[i][k] = str(v).replace('\n', '\a')
    
    rows[i]['期望结果2'] = rows[i].get('期望结果').replace('\a','\n\n')
    rows[i]['期望结果2'] = rows[i]['期望结果2'] +'\n'
    # Add the processed row to rows2
    rows2.append(rows[i])

# Create a DocxTemplate object
doc = DocxTemplate(TEMPLATE_DOCX)
# Prepare the context for rendering
context = {"rows": rows2}

# Render the template and save the output
doc.render(context)
doc.save(OUTPUT)