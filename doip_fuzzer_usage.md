# DoIP 模糊测试工具使用说明

## 功能特性

这个DoIP模糊测试工具已经被修改为：

1. **建立TCP连接** - 自动与目标建立TCP连接
2. **发送路由激活** - 发送标准的DoIP路由激活消息 `02 FD 00 05 00 00 00 0B 0E 80 00 00 00 00 00 FF FF FF FF`
3. **智能模糊测试** - 支持多种模糊测试策略
4. **连接监控** - 监控目标可用性和连接状态
5. **详细日志** - 彩色输出和详细的测试统计

## 使用方法

### 基本用法
```bash
python payload_fuzz.py -t <目标IP>
```

### 完整参数
```bash
python payload_fuzz.py -t <目标IP> -p <端口> -c <测试次数> -d <延迟> -m
```

### 参数说明

- `-t, --target`: **必需** 目标IP地址
- `-p, --port`: 目标端口 (默认: 13400)
- `-c, --count`: 模糊测试用例数量 (默认: 1000)
- `-d, --delay`: 包间延迟秒数 (默认: 0.1)
- `-m, --monitor`: 启用目标可用性监控

### 使用示例

1. **基本测试**
```bash
python payload_fuzz.py -t *************
```

2. **自定义端口和测试次数**
```bash
python payload_fuzz.py -t ************* -p 13400 -c 5000
```

3. **启用监控和自定义延迟**
```bash
python payload_fuzz.py -t ************* -c 2000 -d 0.05 -m
```

## 工作流程

1. **连接建立**: 与目标建立TCP连接
2. **路由激活**: 发送DoIP路由激活消息
3. **模糊测试**: 循环发送变异的诊断消息
4. **响应处理**: 接收和分析目标响应
5. **连接恢复**: 自动处理连接断开和重连
6. **统计报告**: 显示测试结果统计

## 模糊测试策略

### 随机变异 (random)
- 随机修改数据包中的字节
- 适用于发现基本的输入验证问题

### 长度变异 (length)
- 增加或减少数据包长度
- 适用于发现缓冲区溢出等问题

## 输出说明

### 连接状态
- `[  TCP  ]` - TCP连接相关信息
- `[  DoIP  ]` - DoIP协议相关信息

### 测试结果
- `[  0001  ]` - 测试用例编号
- 绿色 - 成功收到响应
- 黄色 - 无响应或超时
- 红色 - 连接错误

### 统计信息
- 成功测试数量
- 失败测试数量
- 总测试数量

## 依赖要求

```bash
pip install scapy construct colorama
```

### Linux额外依赖 (可选)
```bash
pip install pyradamsa  # 高级模糊测试引擎
```

## 注意事项

1. **权限要求**: 在某些系统上可能需要管理员权限
2. **网络安全**: 仅在授权的测试环境中使用
3. **目标影响**: 模糊测试可能导致目标系统不稳定
4. **防火墙**: 确保防火墙允许相关端口通信

## 故障排除

### 连接失败
- 检查目标IP和端口是否正确
- 确认目标服务正在运行
- 检查网络连通性

### 路由激活失败
- 确认目标支持DoIP协议
- 检查路由激活消息格式
- 验证源地址配置

### 测试中断
- 使用Ctrl+C安全中断测试
- 程序会显示当前统计信息
- 连接会自动关闭

## 高级配置

可以修改脚本中的以下参数：

- `base_diagnostic_data`: 基础诊断数据
- `source_addr`: DoIP源地址 (默认: 0x0E80)
- `target_addr`: DoIP目标地址 (默认: 0x1000)
- 超时设置和重连逻辑
