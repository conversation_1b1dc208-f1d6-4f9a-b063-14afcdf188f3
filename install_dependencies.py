#!/usr/bin/env python3
"""
安装国密算法检测系统所需的依赖包
"""

import subprocess
import sys
import os


def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True, check=True)
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败:")
        print(f"  错误信息: {e.stderr}")
        return False


def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"- {package_name} 未安装")
        return False


def main():
    """主函数"""
    print("国密算法检测系统 - 依赖安装脚本")
    print("="*50)
    
    # 必需的依赖包
    required_packages = [
        ('androguard', 'androguard'),
        ('pyelftools', 'pyelftools'),
    ]
    
    # 可选的依赖包（用于增强功能）
    optional_packages = [
        ('requests', 'requests'),
        ('cryptography', 'cryptography'),
    ]
    
    print("\n检查必需依赖...")
    missing_required = []
    for import_name, package_name in required_packages:
        if not check_package(import_name):
            missing_required.append(package_name)
    
    print("\n检查可选依赖...")
    missing_optional = []
    for import_name, package_name in optional_packages:
        if not check_package(import_name):
            missing_optional.append(package_name)
    
    # 安装缺失的包
    if missing_required:
        print(f"\n安装必需依赖包...")
        for package in missing_required:
            if not install_package(package):
                print(f"\n错误: 必需依赖 {package} 安装失败")
                print("请手动安装或检查网络连接")
                sys.exit(1)
    
    if missing_optional:
        print(f"\n安装可选依赖包...")
        for package in missing_optional:
            install_package(package)  # 可选包安装失败不退出
    
    print("\n" + "="*50)
    if not missing_required and not missing_optional:
        print("✓ 所有依赖已安装完成")
    elif not missing_required:
        print("✓ 必需依赖已安装完成")
        if missing_optional:
            print("- 部分可选依赖可能安装失败，但不影响基本功能")
    else:
        print("✓ 依赖安装完成")
    
    print("\n使用说明:")
    print("1. 运行测试: python test_sm_detect.py")
    print("2. 分析APK: python \"SM detect.py\" <apk_file>")
    print("3. 查看帮助: python \"SM detect.py\" --help")


if __name__ == "__main__":
    main()
