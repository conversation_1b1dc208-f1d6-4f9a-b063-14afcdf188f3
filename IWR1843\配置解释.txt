1. 基本控制命令
sensorStop
停止当前传感器活动，确保可以安全地加载新配置。

flushCfg
清空配置，确保之前的配置不会影响当前配置。

sensorStart
启动传感器，开始测量和数据输出。

2. 数据输出模式
dfeDataOutputMode 1
数据前端 (DFE) 输出模式设置为 1，表示实时输出 ADC 数据处理后的检测结果。
3. 信道与 ADC 配置
channelCfg 15 7 0

15：启用所有发射天线 (3 TX) 和接收天线 (4 RX)。
7：配置接收通道，激活 RX1、RX2、RX3 和 RX4。
0：无奇偶校验设置。
adcCfg 2 1

2：设置 ADC 的采样类型为复数采样。
1：每次采样的分辨率为 16 位。
adcbufCfg -1 0 1 1 1

-1：全局配置生效。
0：禁用 ADC 缓冲区奇偶校验。
1 1 1：开启数据格式和缓冲选项。
4. 雷达参数配置
profileCfg 0 77 429 7 57.14 0 0 70 1 256 5209 0 0 30
配置雷达的检测配置文件：

0：配置文件 ID。
77：工作频率，77 GHz。
429：斜率 (MHz/us)，影响距离分辨率。
7：发射脉冲宽度 (us)。
57.14：空闲时间 (us)，脉冲之间的间隔。
0：ADC 起始时间 (us)。
0：高程角度偏移设置。
70：接收增益 (dB)。
1：数字采样数。
256：FFT 点数，影响距离分辨率。
5209：采样率 (ksps)。
0：保留参数。
30：目标检测动态范围。
chirpCfg
定义不同的 chirp 配置：

chirpCfg 0 0 0 0 0 0 0 1：chirp 0 配置，关联 profile 0。
chirpCfg 1 1 0 0 0 0 0 4：chirp 1 配置，关联 profile 1，步长为 4。
chirpCfg 2 2 0 0 0 0 0 2：chirp 2 配置，关联 profile 2，步长为 2。
frameCfg 0 1 16 0 71.429 1 0

0：帧编号。
1：启用 profile 0。
16：每帧 chirp 的数量。
0：帧间隔的间隔延迟。
71.429：帧时间 (ms)。
1：帧的触发方式（1 为自动）。
0：额外选项。
5. CFAR 和检测配置
cfarCfg -1 0 2 8 4 3 0 15 1

-1：配置所有天线。
0：配置范围方向的 CFAR（恒虚警率）。
2：检测窗口。
8：保护单元数。
4：噪声平均窗口。
3：噪声乘子。
15：检测门限 (dB)。
1：允许使用两个目标。
cfarCfg -1 1 0 4 2 3 1 15 1
类似配置，但用于多普勒方向。

6. 目标优化配置
multiObjBeamForming -1 1 0.5
启用多目标波束形成，权重系数为 0.5。

clutterRemoval -1 0
禁用静态杂波移除。

7. 静态校准和调试配置
calibDcRangeSig -1 0 -5 8 256
配置静态目标的直流偏置校准。

compRangeBiasAndRxChanPhase 0.0 1 ...
设置接收通道间的相位和偏移校正。

measureRangeBiasAndRxChanPhase 0 1.5 0.2
测量范围偏移和相位误差。

CQRxSatMonitor 0 3 5 121 0
配置接收通道的饱和监控。

CQSigImgMonitor 0 127 4
配置信号图像监控。

8. 视场与输出配置
aoaFovCfg -1 -90 90 -90 90
配置到达角 (AoA) 的视场范围，从 -90 到 90 度。

cfarFovCfg -1 0 0 8.92
设置范围方向的视场为 8.92 米。

cfarFovCfg -1 1 -1 1.00
设置多普勒方向的视场为 -1 到 1 m/s。

