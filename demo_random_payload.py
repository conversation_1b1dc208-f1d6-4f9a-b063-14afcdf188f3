#!/usr/bin/env python3
"""
演示DoIP随机载荷生成功能
"""

import random
from colorama import init, Fore

def generate_random_doip_payload():
    """生成随机DoIP载荷"""
    # DoIP载荷类型定义
    payload_types = {
        0x0000: "通用DoIP头否定应答",
        0x0001: "车辆识别请求",
        0x0002: "车辆识别请求与EID",
        0x0003: "车辆识别请求与VIN", 
        0x0004: "车辆公告消息",
        0x0005: "路由激活请求",
        0x0007: "存活检查请求",
        0x0008: "存活检查响应",
        0x4001: "DoIP实体状态请求",
        0x4002: "DoIP实体状态响应",
        0x4003: "诊断电源模式信息请求",
        0x4004: "诊断电源模式信息响应",
        0x8001: "诊断消息",
        0x8002: "诊断消息正面应答",
        0x8003: "诊断消息否定应答",
    }
    
    # 随机选择载荷类型
    payload_type = random.choice(list(payload_types.keys()))
    
    # 根据载荷类型生成相应的数据
    if payload_type == 0x0001:  # 车辆识别请求
        data = b""  # 无数据
    elif payload_type == 0x0002:  # 车辆识别请求与EID
        data = bytes([random.randint(0, 255) for _ in range(6)])  # 6字节EID
    elif payload_type == 0x0003:  # 车辆识别请求与VIN
        data = bytes([random.randint(0x30, 0x5A) for _ in range(17)])  # 17字节VIN
    elif payload_type == 0x0005:  # 路由激活请求
        data = bytes([
            random.randint(0, 255), random.randint(0, 255),  # 源地址
            random.randint(0, 255),  # 激活类型
            0x00, 0x00, 0x00, 0x00,  # 保留
            random.randint(0, 255), random.randint(0, 255), 
            random.randint(0, 255), random.randint(0, 255)  # OEM特定
        ])
    elif payload_type == 0x0007:  # 存活检查请求
        data = b""  # 无数据
    elif payload_type == 0x4001:  # DoIP实体状态请求
        data = b""  # 无数据
    elif payload_type == 0x4003:  # 诊断电源模式信息请求
        data = b""  # 无数据
    elif payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息
        # 生成随机诊断数据
        diagnostic_services = [
            [0x10, 0x01],  # 诊断会话控制
            [0x10, 0x02],  # 编程会话
            [0x10, 0x03],  # 扩展诊断会话
            [0x11, 0x01],  # ECU复位 - 硬复位
            [0x22, 0xF1, 0x90],  # 读取数据标识符 - VIN
            [0x22, 0xF1, 0x86],  # 读取数据标识符 - ECU序列号
            [0x27, 0x01],  # 安全访问 - 请求种子
            [0x3E, 0x00],  # 测试器存在
            [0x85, 0x01],  # 控制DTC设置 - 开启
        ]
        
        base_service = random.choice(diagnostic_services)
        # 添加随机数据
        extra_data = [random.randint(0, 255) for _ in range(random.randint(0, 10))]
        data = bytes(base_service + extra_data)
    else:
        # 其他类型生成随机数据
        data_length = random.randint(0, 50)
        data = bytes([random.randint(0, 255) for _ in range(data_length)])
    
    return payload_type, payload_types[payload_type], data

def create_doip_message(payload_type, data=b"", source_addr=0x0E80, target_addr=0x1000):
    """创建DoIP消息"""
    # 根据不同的payload类型计算长度
    if payload_type in [0x8001, 0x8002, 0x8003]:  # 诊断消息类型
        payload_length = 4 + len(data)  # 源地址(2) + 目标地址(2) + 数据长度
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
            (source_addr >> 8) & 0xFF,  # 源地址
            source_addr & 0xFF,
            (target_addr >> 8) & 0xFF,  # 目标地址
            target_addr & 0xFF,
        ])
        return doip_header + data
    else:
        # 其他类型的DoIP消息
        payload_length = len(data)
        doip_header = bytes([
            0x02, 0xFD,  # 协议版本和反向版本
            (payload_type >> 8) & 0xFF,  # 载荷类型 (高字节)
            payload_type & 0xFF,  # 载荷类型 (低字节)
            (payload_length >> 24) & 0xFF,  # 载荷长度
            (payload_length >> 16) & 0xFF,
            (payload_length >> 8) & 0xFF,
            payload_length & 0xFF,
        ])
        return doip_header + data

def main():
    init()  # 初始化colorama
    
    print(Fore.CYAN + "=" * 70)
    print(Fore.CYAN + "              DoIP 随机载荷生成演示")
    print(Fore.CYAN + "=" * 70)
    
    for i in range(10):
        payload_type, payload_name, data = generate_random_doip_payload()
        
        # 创建完整的DoIP消息
        doip_message = create_doip_message(
            payload_type=payload_type,
            data=data,
            source_addr=random.randint(0x0E00, 0x0EFF),
            target_addr=random.randint(0x1000, 0x17FF)
        )
        
        print(Fore.GREEN + f"\n[  {i+1:02d}  ] 载荷类型: {payload_name}")
        print(Fore.WHITE + f"        类型代码: 0x{payload_type:04X}")
        print(Fore.WHITE + f"        数据长度: {len(data)} 字节")
        print(Fore.WHITE + f"        消息长度: {len(doip_message)} 字节")
        
        if len(data) > 0:
            data_hex = " ".join([f"{b:02X}" for b in data[:20]])
            if len(data) > 20:
                data_hex += "..."
            print(Fore.YELLOW + f"        载荷数据: {data_hex}")
        
        # 显示完整DoIP消息的前几个字节
        message_hex = " ".join([f"{b:02X}" for b in doip_message[:16]])
        if len(doip_message) > 16:
            message_hex += "..."
        print(Fore.CYAN + f"        DoIP消息: {message_hex}")
    
    print(Fore.CYAN + "\n" + "=" * 70)
    print(Fore.WHITE + "演示完成！以上展示了10种随机生成的DoIP载荷类型")
    print(Fore.CYAN + "=" * 70)

if __name__ == "__main__":
    main()
