"""
Statistical utilities for dieharder tests.
Contains common statistical functions and distributions used in randomness testing.
"""

import math
import numpy as np
from typing import List, <PERSON><PERSON>, Union
from scipy import stats
from scipy.special import gammainc, gamma


class StatisticsUtils:
    """Collection of statistical utility functions"""
    
    @staticmethod
    def chi_square_test(observed: List[int], expected: List[float] = None) -> <PERSON><PERSON>[float, float]:
        """
        Perform chi-square goodness of fit test
        
        Args:
            observed: Observed frequencies
            expected: Expected frequencies (uniform if None)
            
        Returns:
            Tuple of (chi_square_statistic, p_value)
        """
        observed = np.array(observed)
        
        if expected is None:
            # Assume uniform distribution
            total = np.sum(observed)
            expected = np.full(len(observed), total / len(observed))
        else:
            expected = np.array(expected)
        
        # Avoid division by zero
        expected = np.where(expected == 0, 1e-10, expected)
        
        chi_square = np.sum((observed - expected) ** 2 / expected)
        degrees_of_freedom = len(observed) - 1
        
        p_value = 1 - stats.chi2.cdf(chi_square, degrees_of_freedom)
        
        return chi_square, p_value
    
    @staticmethod
    def kolmogorov_smirnov_test(data: List[float], distribution: str = 'uniform') -> <PERSON><PERSON>[float, float]:
        """
        Perform <PERSON>lmogorov-Smirnov test
        
        Args:
            data: Sample data
            distribution: Expected distribution ('uniform', 'normal')
            
        Returns:
            Tuple of (ks_statistic, p_value)
        """
        data = np.array(data)
        
        if distribution == 'uniform':
            # Test against uniform distribution [0, 1]
            ks_stat, p_value = stats.kstest(data, 'uniform')
        elif distribution == 'normal':
            # Test against standard normal distribution
            ks_stat, p_value = stats.kstest(data, 'norm')
        else:
            raise ValueError(f"Unsupported distribution: {distribution}")
        
        return ks_stat, p_value
    
    @staticmethod
    def anderson_darling_test(data: List[float]) -> Tuple[float, float]:
        """
        Perform Anderson-Darling test for normality
        
        Args:
            data: Sample data
            
        Returns:
            Tuple of (ad_statistic, p_value)
        """
        data = np.array(data)
        result = stats.anderson(data, dist='norm')
        
        # Convert to approximate p-value
        ad_stat = result.statistic
        critical_values = result.critical_values
        significance_levels = result.significance_level
        
        # Approximate p-value calculation
        if ad_stat < critical_values[0]:
            p_value = 1 - significance_levels[0] / 100
        elif ad_stat > critical_values[-1]:
            p_value = significance_levels[-1] / 100
        else:
            # Interpolate
            for i in range(len(critical_values) - 1):
                if critical_values[i] <= ad_stat <= critical_values[i + 1]:
                    ratio = (ad_stat - critical_values[i]) / (critical_values[i + 1] - critical_values[i])
                    p_value = (significance_levels[i] + ratio * (significance_levels[i + 1] - significance_levels[i])) / 100
                    break
        
        return ad_stat, p_value
    
    @staticmethod
    def runs_test(sequence: List[int]) -> Tuple[float, float]:
        """
        Perform runs test for randomness
        
        Args:
            sequence: Binary sequence (0s and 1s)
            
        Returns:
            Tuple of (z_statistic, p_value)
        """
        sequence = np.array(sequence)
        n = len(sequence)
        
        # Count runs
        runs = 1
        for i in range(1, n):
            if sequence[i] != sequence[i-1]:
                runs += 1
        
        # Count 0s and 1s
        n1 = np.sum(sequence == 1)
        n0 = n - n1
        
        if n1 == 0 or n0 == 0:
            return 0.0, 1.0
        
        # Expected number of runs
        expected_runs = (2 * n1 * n0) / n + 1
        
        # Variance of runs
        variance = (2 * n1 * n0 * (2 * n1 * n0 - n)) / (n * n * (n - 1))
        
        if variance <= 0:
            return 0.0, 1.0
        
        # Z-statistic
        z_stat = (runs - expected_runs) / math.sqrt(variance)
        
        # Two-tailed p-value
        p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))
        
        return z_stat, p_value
    
    @staticmethod
    def autocorrelation(sequence: List[int], lag: int = 1) -> float:
        """
        Calculate autocorrelation of a binary sequence
        
        Args:
            sequence: Binary sequence
            lag: Lag for autocorrelation
            
        Returns:
            Autocorrelation coefficient
        """
        sequence = np.array(sequence)
        n = len(sequence)
        
        if lag >= n:
            return 0.0
        
        # Convert to -1, 1 representation
        x = 2 * sequence - 1
        
        # Calculate autocorrelation
        correlation = np.sum(x[:-lag] * x[lag:]) / (n - lag)
        
        return correlation
    
    @staticmethod
    def entropy(data: List[Union[int, str]]) -> float:
        """
        Calculate Shannon entropy
        
        Args:
            data: Input data
            
        Returns:
            Shannon entropy in bits
        """
        from collections import Counter
        
        counts = Counter(data)
        total = len(data)
        
        entropy = 0.0
        for count in counts.values():
            if count > 0:
                p = count / total
                entropy -= p * math.log2(p)
        
        return entropy
    
    @staticmethod
    def gamma_incomplete(a: float, x: float) -> float:
        """
        Calculate incomplete gamma function
        
        Args:
            a: Shape parameter
            x: Upper limit of integration
            
        Returns:
            Incomplete gamma function value
        """
        return gammainc(a, x) * gamma(a)
    
    @staticmethod
    def complementary_error_function(x: float) -> float:
        """
        Calculate complementary error function
        
        Args:
            x: Input value
            
        Returns:
            erfc(x)
        """
        return math.erfc(x)
    
    @staticmethod
    def normal_cdf(x: float, mean: float = 0.0, std: float = 1.0) -> float:
        """
        Calculate normal cumulative distribution function
        
        Args:
            x: Input value
            mean: Mean of distribution
            std: Standard deviation
            
        Returns:
            CDF value
        """
        return stats.norm.cdf(x, loc=mean, scale=std)
    
    @staticmethod
    def binomial_probability(n: int, k: int, p: float) -> float:
        """
        Calculate binomial probability
        
        Args:
            n: Number of trials
            k: Number of successes
            p: Probability of success
            
        Returns:
            Binomial probability
        """
        return stats.binom.pmf(k, n, p)
    
    @staticmethod
    def poisson_probability(k: int, lambda_param: float) -> float:
        """
        Calculate Poisson probability
        
        Args:
            k: Number of events
            lambda_param: Rate parameter
            
        Returns:
            Poisson probability
        """
        return stats.poisson.pmf(k, lambda_param)
