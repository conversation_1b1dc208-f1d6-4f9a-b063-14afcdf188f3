#!/usr/bin/env python3
"""
HackRF设置和测试脚本
用于检测和配置HackRF设备
"""

import subprocess
import sys
import os
import time

def check_hackrf_hardware():
    """检查HackRF硬件连接"""
    print("=== 检查HackRF硬件连接 ===")
    
    # 在Windows上检查USB设备
    try:
        # 使用PowerShell检查USB设备
        cmd = ['powershell', '-Command', 
               'Get-WmiObject -Class Win32_USBHub | Where-Object {$_.Name -like "*HackRF*" -or $_.DeviceID -like "*VID_1D50*"}']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if "HackRF" in result.stdout or "1D50" in result.stdout:
            print("✓ 检测到HackRF设备")
            return True
        else:
            print("✗ 未检测到HackRF设备")
            print("请确保:")
            print("  1. HackRF One已连接到USB端口")
            print("  2. 设备驱动已正确安装")
            print("  3. 设备在设备管理器中显示正常")
            return False
            
    except Exception as e:
        print(f"检查硬件时出错: {e}")
        return False

def check_hackrf_tools():
    """检查HackRF命令行工具"""
    print("\n=== 检查HackRF命令行工具 ===")
    
    tools = ['hackrf_info', 'hackrf_transfer', 'hackrf_debug']
    
    for tool in tools:
        try:
            result = subprocess.run([tool, '--help'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✓ {tool} 可用")
            else:
                print(f"✗ {tool} 不可用")
        except FileNotFoundError:
            print(f"✗ {tool} 未找到")
        except Exception as e:
            print(f"✗ {tool} 检查失败: {e}")

def test_hackrf_info():
    """测试HackRF设备信息"""
    print("\n=== 测试HackRF设备信息 ===")
    
    try:
        result = subprocess.run(['hackrf_info'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ HackRF设备信息:")
            print(result.stdout)
            return True
        else:
            print("✗ 无法获取HackRF设备信息")
            print(f"错误: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("✗ hackrf_info 命令未找到")
        print("请安装HackRF工具包")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def install_hackrf_tools():
    """安装HackRF工具的指导"""
    print("\n=== HackRF工具安装指导 ===")
    print("在Windows上安装HackRF工具:")
    print("1. 下载HackRF Windows工具包:")
    print("   https://github.com/greatscottgadgets/hackrf/releases")
    print("2. 解压到系统PATH目录或添加到PATH环境变量")
    print("3. 安装Zadig驱动程序:")
    print("   https://zadig.akeo.ie/")
    print("4. 使用Zadig为HackRF安装WinUSB驱动")
    print("\n或者使用包管理器:")
    print("- 使用Chocolatey: choco install hackrf")
    print("- 使用vcpkg: vcpkg install hackrf")

def create_test_signal():
    """创建测试信号文件"""
    print("\n=== 创建测试信号 ===")
    
    try:
        import numpy as np
        
        # 创建简单的测试信号
        sample_rate = 2e6
        duration = 0.1  # 100ms
        freq = 1000  # 1kHz测试音
        
        t = np.arange(0, duration, 1/sample_rate)
        signal = np.exp(2j * np.pi * freq * t)
        
        # 转换为HackRF格式
        signal_iq = np.zeros(len(signal) * 2, dtype=np.int8)
        signal_iq[0::2] = (signal.real * 127).astype(np.int8)
        signal_iq[1::2] = (signal.imag * 127).astype(np.int8)
        
        # 保存测试信号
        test_file = "hackrf_test_signal.bin"
        signal_iq.tofile(test_file)
        
        print(f"✓ 测试信号已创建: {test_file}")
        print(f"  信号长度: {len(signal)} 样本")
        print(f"  文件大小: {len(signal_iq)} 字节")
        
        return test_file
        
    except ImportError:
        print("✗ numpy未安装，无法创建测试信号")
        return None
    except Exception as e:
        print(f"✗ 创建测试信号失败: {e}")
        return None

def test_hackrf_transmission(test_file):
    """测试HackRF发送功能"""
    print(f"\n=== 测试HackRF发送功能 ===")
    
    if not test_file or not os.path.exists(test_file):
        print("✗ 测试信号文件不存在")
        return False
    
    print("⚠️  警告: 即将进行射频发送测试")
    print("请确保:")
    print("1. 在屏蔽环境中进行测试")
    print("2. 遵守当地无线电法规")
    print("3. 不干扰其他设备")
    
    confirm = input("确认进行发送测试? (y/N): ")
    if confirm.lower() != 'y':
        print("已取消测试")
        return False
    
    try:
        cmd = [
            'hackrf_transfer',
            '-t', test_file,
            '-f', '433920000',  # 433.92 MHz
            '-s', '2000000',    # 2 MHz采样率
            '-a', '1',          # 启用放大器
            '-x', '20'          # 较低的发送增益用于测试
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("发送测试信号 (5秒)...")
        
        # 限制发送时间
        process = subprocess.Popen(cmd)
        time.sleep(5)  # 发送5秒
        process.terminate()
        
        print("✓ 发送测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 发送测试失败: {e}")
        return False

def main():
    print("🔧 HackRF设置和测试工具")
    print("="*50)
    
    # 检查硬件
    hardware_ok = check_hackrf_hardware()
    
    # 检查工具
    check_hackrf_tools()
    
    # 测试设备信息
    info_ok = test_hackrf_info()
    
    if not info_ok:
        install_hackrf_tools()
        return
    
    # 创建测试信号
    test_file = create_test_signal()
    
    # 测试发送功能
    if test_file:
        test_hackrf_transmission(test_file)
    
    print("\n" + "="*50)
    print("🎯 测试完成!")
    
    if hardware_ok and info_ok:
        print("✅ HackRF设备可以使用")
        print("现在可以运行: python tpms.py --mode brute-force")
    else:
        print("❌ HackRF设备配置有问题")
        print("请按照上述指导安装必要的工具和驱动")
    
    # 清理测试文件
    if test_file and os.path.exists(test_file):
        try:
            os.remove(test_file)
            print(f"已清理测试文件: {test_file}")
        except:
            pass

if __name__ == "__main__":
    main()
