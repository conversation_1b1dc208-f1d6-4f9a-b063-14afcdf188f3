# TPMS信号生成器和发送器

这是一个用于分析、修改和发送胎压监测系统(TPMS)信号的Python工具。支持使用HackRF设备发送射频信号。

## 功能特性

- **信号分析**: 解析TPMS数据包结构
- **CRC暴力破解**: 尝试多种CRC算法验证校验和
- **数据修改**: 修改压力、温度和传感器ID
- **信号生成**: 生成曼彻斯特编码的调频信号
- **HackRF发送**: 使用HackRF设备发送射频信号
- **模拟模式**: 无硬件时保存信号到文件

## 原始数据分析

```
原始数据: 00000000014d0414de0104a6012a06
- wakeup Tone: 0000000001 (唤醒音)
- header byte: 4d (头字节)
- function code: 04 (功能码)
- Acceleration code: 14 (加速度码)
- Sensor id: de0104a6 (传感器ID)
- pressure: 01 (压力值)
- Temperature: 2a (温度值)
- checksum: 06 (校验和)
```

## 安装依赖

```bash
python install_tpms_dependencies.py
```

或手动安装：
```bash
pip install numpy argparse
pip install hackrf  # 可选，用于实际发送
```

## 使用方法

### 1. 交互模式
```bash
python tpms.py
```

### 2. 命令行模式

#### 分析CRC
```bash
python tpms.py --mode analyze
```

#### 修改数据
```bash
# 修改压力和温度
python tpms.py --mode modify --pressure 50 --temperature 25

# 修改传感器ID
python tpms.py --mode modify --sensor-id "12345678"
```

#### 模拟发送
```bash
# 模拟发送原始数据
python tpms.py --mode simulate

# 模拟发送修改后的数据
python tpms.py --mode simulate --pressure 100 --temperature 30
```

#### 实际发送 (需要HackRF)
```bash
# 发送原始数据
python tpms.py --mode transmit

# 发送修改后的数据，重复3次
python tpms.py --mode transmit --pressure 80 --temperature 35 --repeat 3

# 自定义频率发送
python tpms.py --mode transmit --freq 315000000  # 315 MHz
```

#### CRC暴力破解发送
```bash
# 暴力破解所有CRC值 (0x00-0xFF)
python tpms.py --mode brute-force

# 暴力破解指定范围的CRC值
python tpms.py --mode brute-force --start-crc 00 --end-crc 0f --crc-interval 0.5

# 暴力破解修改后的数据
python tpms.py --mode brute-force --pressure 100 --temperature 30 --start-crc f0 --end-crc ff

# 暴力破解自定义数据
python tpms.py --mode brute-force --data 00000000014d0414de0104a6012a --start-crc 00 --end-crc ff
```

## 命令行参数

- `--mode`: 运行模式 (analyze/modify/transmit/simulate)
- `--pressure`: 压力值 (0-255)
- `--temperature`: 温度值 (0-255)
- `--sensor-id`: 传感器ID (8位十六进制)
- `--repeat`: 发送重复次数
- `--interval`: 发送间隔(秒)
- `--freq`: 载波频率(Hz)
- `--data`: 自定义数据包(十六进制)
- `--start-crc`: 暴力破解起始CRC值(十六进制)
- `--end-crc`: 暴力破解结束CRC值(十六进制)
- `--crc-interval`: CRC暴力破解发送间隔(秒)

## 批量管理器

使用 `tpms_batch_manager.py` 管理暴力破解生成的多个信号文件：

```bash
# 列出所有CRC文件
python tpms_batch_manager.py --action list

# 分析CRC覆盖范围
python tpms_batch_manager.py --action analyze

# 批量发送所有CRC文件 (需要HackRF)
python tpms_batch_manager.py --action transmit --interval 1.0

# 生成GNU Radio播放列表
python tpms_batch_manager.py --action playlist

# 清理生成的文件
python tpms_batch_manager.py --action cleanup
```

## 技术细节

### 信号参数
- 载波频率: 433.92 MHz (可配置)
- 采样率: 2 MHz
- 比特率: 10 kbps
- 调制方式: 频率调制 (FM)
- 编码方式: 曼彻斯特编码

### CRC算法
程序会尝试以下CRC算法：
1. CRC8 (多项式: 0x07)
2. 简单校验和
3. XOR校验和

### 数据格式
```
[唤醒音 5字节][头字节 1字节][功能码 1字节][加速度码 1字节][传感器ID 4字节][压力 1字节][温度 1字节][校验和 1字节]
```

## 注意事项

1. **法律合规**: 仅用于研究和测试目的，请遵守当地法律法规
2. **频率许可**: 确保在允许的频段内发送信号
3. **设备安全**: 使用适当的发送功率，避免干扰其他设备
4. **HackRF驱动**: 确保HackRF设备驱动正确安装

## 故障排除

### HackRF相关问题
- 确保HackRF设备正确连接
- 检查驱动程序是否安装
- 在Linux上可能需要udev规则

### 依赖问题
- 如果hackrf包安装失败，可以使用模拟模式
- 确保numpy版本兼容

## 示例输出

```
=== TPMS信号分析和发送工具 ===
原始数据: 00000000014d0414de0104a6012a06
- 唤醒音: 0000000001
- 头字节: 4d
- 功能码: 04
- 加速度码: 14
- 传感器ID: de0104a6
- 压力: 01 (1)
- 温度: 2a (42)
- 校验和: 06

=== CRC分析模式 ===
开始暴力破解CRC校验和...
✓ CRC8: 计算值=06, 目标值=06 - 匹配!
✗ Simple Sum: 计算值=4a, 目标值=06 - 不匹配
✗ XOR: 计算值=4a, 目标值=06 - 不匹配
```
