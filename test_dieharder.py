#!/usr/bin/env python3
"""
Test script for the dieharder implementation.
This script runs a few basic tests to verify the implementation works.
"""

import sys
import time
from dieharder import <PERSON><PERSON><PERSON>er


def test_basic_functionality():
    """Test basic functionality of the dieharder implementation"""
    print("Testing basic dieharder functionality...")
    
    # Create dieharder instance
    dh = DieHarder()
    
    # Setup generator
    print("Setting up MT19937 generator...")
    success = dh.setup_generator('mt19937', seed=12345)
    if not success:
        print("Failed to setup generator!")
        return False
    
    print(f"Generator setup successful: {dh.current_generator}")
    
    # Test performance measurement
    print("Testing performance measurement...")
    perf = dh.current_generator.get_performance()
    print(f"Generator performance: {perf:.2e} randoms/second")
    
    # Test basic random generation
    print("Testing random number generation...")
    for i in range(5):
        rand_val = dh.current_generator.random_uint32()
        print(f"Random value {i+1}: {rand_val:08X}")
    
    return True


def test_simple_tests():
    """Run a few simple tests with small sample sizes"""
    print("\nRunning simple tests with small sample sizes...")
    
    dh = DieHarder()
    dh.setup_generator('mt19937', seed=12345)
    
    # Print header
    dh.print_header('mt19937', 12345)
    
    # Run a few quick tests
    test_configs = [
        ('diehard_birthdays', {'tsamples': 50, 'psamples': 10}),
        ('sts_monobit', {'tsamples': 1000, 'psamples': 10}),
        ('rgb_bitdist', {'tsamples': 1000, 'psamples': 10}),
    ]
    
    for test_name, kwargs in test_configs:
        print(f"\nRunning {test_name}...")
        try:
            start_time = time.time()
            result = dh.run_test(test_name, **kwargs)
            end_time = time.time()
            
            if result:
                print(f"Test completed in {end_time - start_time:.2f} seconds")
                print(f"Result: {result}")
            else:
                print(f"Test {test_name} failed!")
        except Exception as e:
            print(f"Error running {test_name}: {e}")
    
    return True


def test_list_functionality():
    """Test the list functionality"""
    print("\nTesting list functionality...")
    
    dh = DieHarder()
    dh.list_tests()
    
    return True


def main():
    """Main test function"""
    print("=" * 60)
    print("DieHarder Python Implementation Test Suite")
    print("=" * 60)
    
    # Test basic functionality
    if not test_basic_functionality():
        print("Basic functionality test failed!")
        return 1
    
    # Test list functionality
    if not test_list_functionality():
        print("List functionality test failed!")
        return 1
    
    # Test simple tests
    if not test_simple_tests():
        print("Simple tests failed!")
        return 1
    
    print("\n" + "=" * 60)
    print("All tests completed successfully!")
    print("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
