"""
NIST Statistical Test Suite (STS) tests for random number generators.
These tests are part of the NIST SP 800-22 standard.
"""

import math
import numpy as np
from typing import Dict, List, Optional
from collections import Counter

from generators.base_generator import BaseGenerator
from utils.statistics import StatisticsUtils


class STSTests:
    """Implementation of NIST STS tests"""
    
    def __init__(self, generator: BaseGenerator):
        self.generator = generator
        self.stats = StatisticsUtils()
    
    def sts_monobit(self, tsamples: int = 100000, psamples: int = 100) -> Dict:
        """
        Monobit test - tests the proportion of ones and zeros.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate bit sequence
            bits = self.generator.random_bits(tsamples)
            
            # Count ones
            ones = sum(bits)
            
            # Calculate test statistic
            s_obs = abs(ones - tsamples/2) / math.sqrt(tsamples/4)
            
            # Calculate p-value using complementary error function
            p_val = self.stats.complementary_error_function(s_obs / math.sqrt(2))
            p_values.append(p_val)
        
        # Combine p-values
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'sts_monobit',
            'ntup': 1,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def sts_runs(self, tsamples: int = 100000, psamples: int = 100) -> Dict:
        """
        Runs test - tests the total number of runs in the sequence.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate bit sequence
            bits = self.generator.random_bits(tsamples)
            
            # Pre-test: check if proportion of ones is reasonable
            ones = sum(bits)
            pi = ones / tsamples
            
            if abs(pi - 0.5) >= 2/math.sqrt(tsamples):
                # Sequence fails pre-test
                p_values.append(0.0)
                continue
            
            # Count runs
            runs = 1
            for i in range(1, tsamples):
                if bits[i] != bits[i-1]:
                    runs += 1
            
            # Calculate test statistic
            expected_runs = 2 * tsamples * pi * (1 - pi) + 1
            variance = 2 * tsamples * pi * (1 - pi) * (2 * tsamples * pi * (1 - pi) - 1)
            
            if variance > 0:
                z = (runs - expected_runs) / math.sqrt(variance)
                p_val = self.stats.complementary_error_function(abs(z) / math.sqrt(2))
            else:
                p_val = 0.5
            
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'sts_runs',
            'ntup': 2,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def sts_serial(self, tsamples: int = 100000, psamples: int = 100, m: int = 16) -> Dict:
        """
        Serial test - tests the frequency of overlapping m-bit patterns.
        """
        p_values = []
        
        for _ in range(psamples):
            # Generate bit sequence
            bits = self.generator.random_bits(tsamples)
            
            # Calculate psi_m values for m, m-1, m-2
            psi_m = self._calculate_psi(bits, m)
            psi_m1 = self._calculate_psi(bits, m-1) if m > 1 else 0
            psi_m2 = self._calculate_psi(bits, m-2) if m > 2 else 0
            
            # Calculate test statistics
            delta1 = psi_m - psi_m1
            delta2 = psi_m - 2*psi_m1 + psi_m2
            
            # Calculate p-values
            p_val1 = self.stats.gamma_incomplete(2**(m-2), delta1/2) / math.gamma(2**(m-2))
            p_val2 = self.stats.gamma_incomplete(2**(m-3), delta2/2) / math.gamma(2**(m-3)) if m > 2 else 1.0
            
            # Use the minimum p-value (most conservative)
            p_val = min(p_val1, p_val2)
            p_values.append(p_val)
        
        combined_p = self._combine_p_values(p_values)
        
        return {
            'name': 'sts_serial',
            'ntup': m,
            'tsamples': tsamples,
            'psamples': psamples,
            'p_value': combined_p
        }
    
    def _calculate_psi(self, bits: List[int], m: int) -> float:
        """Calculate psi_m statistic for serial test"""
        n = len(bits)
        
        if m <= 0 or m > 20:  # Reasonable limits
            return 0.0
        
        # Count m-bit patterns
        pattern_counts = Counter()
        
        for i in range(n):
            pattern = 0
            for j in range(m):
                bit_index = (i + j) % n  # Circular
                pattern = (pattern << 1) | bits[bit_index]
            pattern_counts[pattern] += 1
        
        # Calculate psi_m
        psi = 0.0
        for count in pattern_counts.values():
            psi += count * count
        
        psi = (2**m / n) * psi - n
        
        return psi
    
    def _combine_p_values(self, p_values: List[float]) -> float:
        """Combine multiple p-values using Fisher's method"""
        if not p_values:
            return 0.5
        
        # Fisher's combined probability test
        chi_sq = -2 * sum(math.log(max(p, 1e-10)) for p in p_values)
        df = 2 * len(p_values)
        
        # Convert to p-value
        from scipy.stats import chi2
        combined_p = 1 - chi2.cdf(chi_sq, df)
        
        return max(min(combined_p, 1.0), 0.0)
