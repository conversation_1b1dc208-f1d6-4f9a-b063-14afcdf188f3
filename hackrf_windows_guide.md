# Windows上使用HackRF One的完整指南

## 当前状态
✅ **HackRF One硬件已连接**  
❌ **HackRF工具未安装**  
❌ **Python库不可用**  

## 解决方案

### 方案1: 安装HackRF工具包 (推荐)

#### 步骤1: 下载HackRF工具
1. 访问: https://github.com/greatscottgadgets/hackrf/releases
2. 下载最新的Windows版本 (例如: `hackrf-win64-2023.01.1.zip`)
3. 解压到 `C:\hackrf\` 目录

#### 步骤2: 添加到系统PATH
1. 打开"系统属性" → "高级" → "环境变量"
2. 在"系统变量"中找到"Path"
3. 添加 `C:\hackrf\bin` 到PATH

#### 步骤3: 安装驱动 (使用Zadig)
1. 下载Zadig: https://zadig.akeo.ie/
2. 运行Zadig，选择HackRF设备
3. 安装WinUSB驱动

#### 步骤4: 验证安装
```cmd
hackrf_info
```

### 方案2: 使用GNU Radio (替代方案)

如果HackRF工具安装困难，可以使用GNU Radio:

1. 安装GNU Radio: https://www.gnuradio.org/
2. 使用GNU Radio Companion加载生成的.bin文件
3. 配置osmocom Sink块连接HackRF

### 方案3: 使用SDR# + 插件

1. 安装SDR#: https://airspy.com/download/
2. 安装HackRF插件
3. 使用文件播放功能

## 当前程序状态

你的TPMS程序已经完全准备好了：

### ✅ 已实现的功能
- CRC暴力破解 (0x00-0xFF)
- 信号生成和调制
- 文件保存功能
- HackRF发送支持 (一旦工具可用)

### 📁 生成的文件
程序已经生成了多个CRC变体的信号文件：
- `tpms_crc_00.bin` - CRC=0x00的信号
- `tpms_crc_01.bin` - CRC=0x01的信号
- `tpms_crc_02.bin` - CRC=0x02的信号
- ... (等等)

### 🚀 使用这些文件

一旦HackRF工具安装完成，你可以：

```bash
# 发送单个CRC变体
hackrf_transfer -t tpms_crc_06.bin -f 433920000 -s 2000000 -a 1 -x 47

# 或者使用程序自动发送
python tpms.py --mode brute-force
```

## 测试步骤

### 1. 验证硬件连接
```cmd
# 在设备管理器中查看HackRF设备
devmgmt.msc
```

### 2. 安装工具后测试
```cmd
# 获取设备信息
hackrf_info

# 测试发送 (小心使用!)
hackrf_transfer -t tpms_crc_06.bin -f 433920000 -s 2000000 -a 1 -x 20
```

### 3. 使用TPMS程序
```bash
# 完整的CRC暴力破解
python tpms.py --mode brute-force --start-crc 00 --end-crc ff --crc-interval 0.5
```

## 重要提醒

⚠️ **安全和法律考虑**:
- 仅在屏蔽环境中测试
- 遵守当地无线电法规
- 不要干扰正常的TPMS系统
- 使用适当的发送功率

## 故障排除

### 问题1: 设备未识别
- 检查USB连接
- 重新安装驱动
- 尝试不同的USB端口

### 问题2: 工具无法运行
- 确认PATH设置正确
- 以管理员身份运行
- 检查防火墙设置

### 问题3: 发送失败
- 降低发送功率
- 检查频率设置
- 确认信号文件格式

## 下一步

1. **立即可做**: 使用生成的.bin文件配合其他SDR软件
2. **推荐安装**: HackRF官方工具包
3. **最终目标**: 使用程序的完整暴力破解功能

你的TPMS暴力破解程序已经完全准备好了，只需要HackRF工具支持即可开始实际的射频发送！
