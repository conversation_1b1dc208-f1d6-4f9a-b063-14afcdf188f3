打开TLS：
send 1003 1002 2709 270a --s2k 2e337701


关闭TLS：
send 1003 1002 2709 270a --s2k 3101338601 2e337700


请求OBD防火墙状态：
send 1003 2709 270a --s2k 31033386 223375
send 1003 2709 270a --s2k 223375

SSH disable:
send 1003 1002 2709 270a --s2k 310133870000


SSH enable:
send 1003 2709 270a --s2k 310133870001

OBD防火墙disable：
send 1003 2709 270a --s2k 3101338601
OBD防火墙enable：
send 1003 2709 270a --s2k 31023386


整车：
connect ********** 3496 Tls_DoipOver_test.crt Tls_DoipOver_test.key

set -t 0005 -k 9f27950ebab8b64a74457341a07b06ba --mask 7fd2  VDF
set -t 005f -k b13e732a32eae5f83811363afb00c7b2 --mask 6bca  SAF 
set -t 0023 -k bb9d9074d791f3921a6ac8029c9c1181 --mask 3689  ADC
set -t 0032 -k 55daeeee15e098d698803bb12742eadb --mask 19db  CDC


零部件（HJNFBABN3PP000088）：
set -t 0005 -k 004dd6941a831555f80125a3d79ff028 --mask 7fd2  VDF
set -t 005f -k 65eabe41f1762a1fe03a076b4bd1865e --mask 6bca  SAF 
set -t 0023 -k 10bb4083d1faa25120b27c8fc5f695f0 --mask 3689  ADC
set -t 0032 -k dc17a869df242b0376a128741508689b --mask 19db  CDC

默认AES key：
set -k c54d4577632faa6a2747d131b9210666
配置字：
2EF1000000300200000000000000000000000000800C001100000000000400080700000000000000000000000300001400000050000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000502290132510000081800000021001D000000FFFFFF0F0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000728C
2EF1000000300200000000000000000000000000800733113300000000040000075505551505005505050000910500040000050000000000000000000000000000000000000000000000000000000000000000000000000000000000000001100000010000000000013290132510000082800000021001D000000FF7FF10F00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000079C
set -t 0023 -k c54d4577632faa6a2747d131b9210666 --mask 3689  ADC
set -t 0032 -k c54d4577632faa6a2747d131b9210666 --mask 19db