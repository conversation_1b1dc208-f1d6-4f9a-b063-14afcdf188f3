#!/usr/bin/env python3
"""
UART嗅探器使用示例
展示如何在实际项目中使用UARTSniffer类
"""

import time
import signal
import sys
from uart import UARTSniffer
from uart_config import get_config, create_test_data

def signal_handler(sig, frame):
    """信号处理器，用于优雅退出"""
    print('\n收到退出信号，正在停止...')
    sys.exit(0)

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建嗅探器实例
    sniffer = UARTSniffer(
        port='COM3',
        baudrate=115200,
        idle_timeout=1.0,
        send_data=b'\xAA\xBB\xCC\xDD'
    )
    
    try:
        # 启动嗅探
        if sniffer.start_sniffing():
            print("嗅探器已启动，运行10秒...")
            
            # 运行10秒
            for i in range(10):
                time.sleep(1)
                print(f"运行中... {i+1}/10秒")
            
            print("基本示例完成")
        else:
            print("无法启动嗅探器")
            
    finally:
        sniffer.stop_sniffing()


def example_with_config():
    """使用配置文件的示例"""
    print("\n=== 使用配置文件示例 ===")
    
    # 使用预设配置
    config = get_config('debug')
    config['port'] = 'COM3'  # 根据实际情况修改
    
    sniffer = UARTSniffer(**config)
    
    try:
        if sniffer.start_sniffing():
            print("使用debug配置启动嗅探器...")
            time.sleep(5)
            print("配置示例完成")
        else:
            print("无法启动嗅探器")
    finally:
        sniffer.stop_sniffing()


def example_interactive_control():
    """交互控制示例"""
    print("\n=== 交互控制示例 ===")
    
    sniffer = UARTSniffer(
        port='COM3',
        baudrate=115200,
        idle_timeout=2.0,
        send_data=create_test_data('alternating', 8)
    )
    
    try:
        if sniffer.start_sniffing():
            print("嗅探器已启动")
            print("命令: 's'=手动发送, 'q'=退出")
            
            while True:
                try:
                    cmd = input("输入命令: ").strip().lower()
                    
                    if cmd == 'q':
                        break
                    elif cmd == 's':
                        # 手动发送不同的数据
                        test_data = create_test_data('counter', 6)
                        sniffer.send_manual_data(test_data)
                        print(f"已发送: {test_data.hex()}")
                    else:
                        print("未知命令")
                        
                except (EOFError, KeyboardInterrupt):
                    break
                    
            print("交互示例完成")
        else:
            print("无法启动嗅探器")
    finally:
        sniffer.stop_sniffing()


def example_data_processing():
    """数据处理示例"""
    print("\n=== 数据处理示例 ===")
    
    class DataProcessor:
        def __init__(self):
            self.packet_count = 0
            self.total_bytes = 0
            
        def process_data(self, sniffer):
            """处理接收到的数据"""
            while not sniffer.stop_event.is_set():
                try:
                    if not sniffer.data_queue.empty():
                        timestamp, data = sniffer.data_queue.get(timeout=0.1)
                        self.packet_count += 1
                        self.total_bytes += len(data)
                        
                        # 简单的数据分析
                        if data.startswith(b'\xAA'):
                            print(f"检测到特殊数据包: {data.hex()}")
                        
                        # 每10个包显示统计
                        if self.packet_count % 10 == 0:
                            print(f"统计: {self.packet_count}包, {self.total_bytes}字节")
                            
                except:
                    pass
                    
                time.sleep(0.01)
    
    sniffer = UARTSniffer(
        port='COM3',
        baudrate=115200,
        idle_timeout=0.5,
        send_data=b'\xAA\xBB\xCC\xDD\xEE\xFF'
    )
    
    processor = DataProcessor()
    
    try:
        if sniffer.start_sniffing():
            print("启动数据处理示例...")
            
            # 在单独线程中处理数据
            import threading
            process_thread = threading.Thread(
                target=processor.process_data, 
                args=(sniffer,), 
                daemon=True
            )
            process_thread.start()
            
            # 运行5秒
            time.sleep(5)
            
            print(f"最终统计: {processor.packet_count}包, {processor.total_bytes}字节")
            print("数据处理示例完成")
        else:
            print("无法启动嗅探器")
    finally:
        sniffer.stop_sniffing()


def example_multiple_patterns():
    """多种数据模式示例"""
    print("\n=== 多种数据模式示例 ===")
    
    patterns = ['counter', 'alternating', 'binary', 'ascii']
    
    for pattern in patterns:
        print(f"\n测试 {pattern} 模式...")
        
        test_data = create_test_data(pattern, 8)
        print(f"数据: {test_data.hex()} ({pattern})")
        
        sniffer = UARTSniffer(
            port='COM3',
            baudrate=115200,
            idle_timeout=0.3,
            send_data=test_data
        )
        
        try:
            if sniffer.start_sniffing():
                time.sleep(1)  # 短暂运行
            else:
                print(f"无法启动嗅探器 ({pattern})")
        finally:
            sniffer.stop_sniffing()
    
    print("多模式示例完成")


def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    
    print("UART嗅探器使用示例")
    print("注意: 请确保串口设备已连接并配置正确的端口号")
    
    examples = [
        ("1", "基本使用", example_basic_usage),
        ("2", "配置文件", example_with_config),
        ("3", "交互控制", example_interactive_control),
        ("4", "数据处理", example_data_processing),
        ("5", "多种模式", example_multiple_patterns),
    ]
    
    print("\n可用示例:")
    for num, name, _ in examples:
        print(f"  {num}. {name}")
    
    try:
        choice = input("\n选择示例 (1-5, 或回车运行所有): ").strip()
        
        if choice == "":
            # 运行所有示例
            for _, name, func in examples:
                print(f"\n{'='*50}")
                print(f"运行示例: {name}")
                print('='*50)
                try:
                    func()
                except Exception as e:
                    print(f"示例 {name} 出错: {e}")
                time.sleep(1)  # 示例间隔
        else:
            # 运行指定示例
            for num, name, func in examples:
                if choice == num:
                    print(f"\n运行示例: {name}")
                    func()
                    break
            else:
                print("无效选择")
                
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"运行出错: {e}")
    
    print("\n所有示例完成")


if __name__ == "__main__":
    main()
